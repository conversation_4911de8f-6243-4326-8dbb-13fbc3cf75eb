/* Add these styles to your existing CSS file or create a new one */

.tdk-pagination {
    list-style: none;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 60px;
    margin-bottom: 30px;
  }
  
  .tdk-pagination li {
    margin-right: 10px;
  }
  
  .tdk-pagination .pagination__link {
    text-decoration: none;
    color: #333;
    font-size: 16px;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
  }
  
  .tdk-pagination .pagination__link.active {
    background-color: #007bff;
    color: #fff;
    border-color: #007bff;
  }
  
  .tdk-pagination .pagination__link:hover {
    background-color: #0056b3;
    color: #fff;
    border-color: #0056b3;
  }
  
  .tdk-pagination i {
    font-size: 16px;
  }
  
  /* Optional: Add animation for the wow fadeInDown effect */
  .wow.fadeInDown {
    visibility: visible !important;
    animation: fadeInDown 1s;
  }
  
  @keyframes fadeInDown {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  