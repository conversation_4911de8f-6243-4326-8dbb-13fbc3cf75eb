/* Custom styles for the application */

/* image popup */
img.mfp-img {
  box-shadow: 0 0 8px rgb(0 0 0 / 60%);
  position: absolute;
  max-height: 392px;
  padding: 0 !important;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.mfp-img-container .mfp-content {
  max-width: 400px !important;
}
.mfp-img-container .mfp-close {
  top: -110px;
  right: -24px;
}

img {
  max-width: 100%;
  display: block;
  object-position: center;
}

/* Custom Logo Styles for Better Integration */
.brand-logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-logo img {
  transition: all 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.brand-logo:hover img {
  transform: scale(1.05);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
}

/* Header Logo Styles */
.header-area .brand-logo img {
  max-height: 60px;
  width: auto;
}

.header-area.transparent-header .brand-logo img {
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3));
}

/* Mobile Logo Styles */
.mobile-logo .brand-logo img {
  max-height: 50px;
  width: auto;
}

/* Footer Logo Styles */
.footer-logo img {
  max-height: 70px;
  width: auto;
  transition: opacity 0.3s ease;
}

.footer-logo:hover img {
  opacity: 0.8;
}

/* Logo Background Removal Support */
.logo-transparent {
  background: transparent !important;
}

/* Responsive Logo Adjustments */
@media (max-width: 768px) {
  .header-area .brand-logo img {
    max-height: 45px;
  }

  .mobile-logo .brand-logo img {
    max-height: 40px;
  }

  .footer-logo img {
    max-height: 60px;
  }
}

@media (max-width: 480px) {
  .header-area .brand-logo img {
    max-height: 40px;
  }

  .footer-logo img {
    max-height: 50px;
  }
}

/* Logo Loading Animation */
.brand-logo img {
  opacity: 0;
  animation: logoFadeIn 0.6s ease-in-out forwards;
}

@keyframes logoFadeIn {
  to {
    opacity: 1;
  }
}

/* High DPI Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .brand-logo img,
  .footer-logo img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

:root {
  --yellow-primary: #fef200;
  --hover-bg: black;
}

.btn-custom-yellow {
  background-color: var(--yellow-primary);
  color: black;
  border: 0;
  transition: all 0.3s ease-in-out;
}

.btn-custom-yellow:hover,
.btn-custom-yellow:focus {
  background-color: var(--hover-bg) !important;
  color: var(--yellow-primary) !important;
  border-color: transparent;
}