export default function Description({ title, description, features }) {
    return (
      <section className="destination-details-section pt-50">
        <div className="container">
          <div className="destination-details-wrapper">
            <div className="destination-info wow fadeInUp">
              <h3 className="title">{title}</h3>
              <div className="meta"></div>
              <p>{description}</p>
              <ul className="features-list mb-40">
                {features.map((feature, index) => (
                  <li key={index}>
                    <span>
                      <i className="fas fa-badge-check" />
                      {feature}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>
    );
  }
  