import ThingsToDoPage from "../../components/ThingsToDoPage/thingstodo";
import ThingsToDoBanner from "../../components/ThingsToDoBanner";
import Layout from "../../layout/Layout";
import Link from "next/link";

export const metadata = {
  title: "Coisas para Fazer | Descubra Cabo Verde",
  description: "Descubra coisas para fazer em Cabo Verde, desde eventos até atividades incríveis",
};

const ThingsToDo = () => {
  return (
    <Layout header={2} extraClass={"pt-10"} >
        <ThingsToDoBanner pageTitle={"Coisas para Fazer"} />
        {/*====== Start Destination Section ======*/}
        <ThingsToDoPage/>

        {/*====== End Destination Section ======*/}
        {/*====== Start CTA Section ======*/}
        <section
          className="cta-bg overlay bg_cover pt-150 pb-150"
          style={{ backgroundImage: "url(assets/images/bg/cta-bg.jpg)" }}
        >
          <div className="container">
            <div className="row align-items-center">
              <div className="col-xl-7 col-lg-8">
                {/*=== CTA Content Box ===*/}
                <div className="cta-content-box text-white wow fadeInLeft">
                  <h2 className="mb-35">
                    Pronto para Viajar com Aventura Real e Desfrutar da Natureza
                  </h2>
                  <Link href="/about">
                    <div className="main-btn secondary-btn">
                      Verificar Disponibilidade
                      <i className="far fa-paper-plane" />
                    </div>
                  </Link>
                </div>
              </div>
              <div className="col-xl-5 col-lg-4">
                {/*=== Play Box ===*/}
                <div className="play-box text-lg-end text-center wow fadeInRight">

                </div>
              </div>
            </div>
          </div>
        </section>
      </Layout>
  );
};
export default ThingsToDo;