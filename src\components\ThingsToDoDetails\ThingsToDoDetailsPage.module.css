.detailsSection {
    padding: 50px 0;
    background-color: #f8f9fa; /* Light background for better contrast */
  }
  
  .activityImage {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  .activityImage img {
    width: 100%;
    height: auto;
    object-fit: cover;
  }
  
  .activityTitle {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 20px;
  }
  
  .activityTexts {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #555;
    margin-bottom: 30px;
  }
  
  .overviewTable {
    margin-top: 20px;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  .overviewTable td {
    font-size: 1rem;
    padding: 10px 15px;
  }
  
  .overviewTable tr:nth-child(even) {
    background-color: #f9f9f9;
  }
  
  .sidebar {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-top: 30px;
  }
  
  .sidebar h4 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
  }
  
  .sidebar p {
    font-size: 1rem;
    line-height: 1.5;
    color: #555;
  }
  
  .accordion-item {
    margin-top: 10px;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .accordion-header {
    background-color: #fa4836;
    color: #fff;
    padding: 10px 15px;
    font-size: 1rem;
  }
  
  .accordion-button:not(.collapsed) {
    color: #fff;
    background-color: #fa4836;
  }
  
  .accordion-body {
    background-color: #f8f9fa;
    color: #555;
    padding: 15px;
    font-size: 1rem;
    line-height: 1.6;
  }
  