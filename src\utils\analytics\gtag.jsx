'use client'

export const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS

export const pageview = (url) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', GA_TRACKING_ID, {
      page_path: url,
      page_title: document.title,
      page_location: window.location.href
    })
  }
}

export const event = ({ action, category, label, value, custom_parameters = {} }) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
      ...custom_parameters
    })
  }
}

// Enhanced tracking functions
export const trackBusinessContact = (contactType, businessInfo = {}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'contact', {
      event_category: 'Business Contact',
      event_label: contactType,
      value: businessInfo.businessName || 'Unknown',
      business_name: businessInfo.businessName,
      business_category: businessInfo.category,
      contact_method: contactType
    })
  }
}

export const trackEngagement = (engagementType, details = {}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', engagementType, {
      event_category: 'User Engagement',
      event_label: details.label || engagementType,
      value: details.value,
      page_path: window.location.pathname,
      ...details
    })
  }
}

export const trackConversion = (conversionType, conversionData = {}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'conversion', {
      event_category: 'Conversion',
      event_label: conversionType,
      value: conversionData.value || 1,
      currency: 'KES',
      ...conversionData
    })
  }
}

export const trackUserTiming = (timingCategory, timingVar, value, label = '') => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'timing_complete', {
      name: timingVar,
      value: value,
      event_category: timingCategory,
      event_label: label
    })
  }
}

// E-commerce tracking (for future use)
export const trackPurchase = (transactionId, items, value, currency = 'KES') => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'purchase', {
      transaction_id: transactionId,
      value: value,
      currency: currency,
      items: items
    })
  }
}

export const trackAddToCart = (item) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'add_to_cart', {
      currency: 'KES',
      value: item.value || 0,
      items: [item]
    })
  }
}
