import destinationDetails from '../data/destinationDetails'
import thingsToDo from '../data/thingsToDo'
import blogData from '../data/blogData'

// Helper function to safely parse dates
function getSafeDate(dateString, fallback) {
  if (!dateString) return fallback

  // Try to parse the date string
  const parsedDate = new Date(dateString)

  // Check if the date is valid
  if (isNaN(parsedDate.getTime())) {
    // If date string is in Portuguese format like "15 de Novembro, 2023"
    // or any other invalid format, return fallback
    return fallback
  }

  return parsedDate
}

export default function sitemap() {
  const baseUrl = 'https://www.guiadecaboverde.cv'
  const currentDate = new Date()

  // Static pages
  const staticPages = [
    {
      url: baseUrl,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 1.0,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/hotels`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/restaurants`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/destinations`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/things-to-do`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/places`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/blog`,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/faqs`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}/privacy-policy`,
      lastModified: currentDate,
      changeFrequency: 'yearly',
      priority: 0.3,
    },
  ]

  // Dynamic destination pages
  const destinationPages = destinationDetails.map((destination) => ({
    url: `${baseUrl}/destinations-details/${destination.id}`,
    lastModified: currentDate,
    changeFrequency: 'monthly',
    priority: 0.7,
  }))

  // Dynamic things-to-do pages
  const thingsToDoPages = thingsToDo.map((activity) => ({
    url: `${baseUrl}/things-to-do-details/${activity.id}`,
    lastModified: currentDate,
    changeFrequency: 'monthly',
    priority: 0.7,
  }))

  // Dynamic blog pages with safe date parsing
  const blogPages = blogData.map((post) => ({
    url: `${baseUrl}/blog/${post.slug}`,
    lastModified: getSafeDate(post.meta?.date, currentDate),
    changeFrequency: 'monthly',
    priority: 0.6,
  }))

  return [
    ...staticPages,
    ...destinationPages,
    ...thingsToDoPages,
    ...blogPages,
  ]
}