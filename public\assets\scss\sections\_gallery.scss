

/*---==================
    10. Gallery css 
=================----*/

.single-gallery-item{
    &:hover{
        & .gallery-img{
            & .hover-overlay{
                visibility: visible;
                opacity: 1;
            }
            & .icon-btn{
                @include transform(translateY(0px));
            }
        }
    }
    & .gallery-img{
        position: relative;
        overflow: hidden;
        & img{
            width: 100%;
            @include border-radius(7px);
        }
        & .hover-overlay{
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            @include border-radius(7px);
            @extend %flex-center;
            visibility: hidden;
            opacity: 0;
            @include transition(.3s);
        }
        & .icon-btn{
            width: 65px;
            height: 65px;
            @include border-radius(50%);
            @extend %secondary-bg;
            @extend %flex-center;
            @extend %white-color;
            font-size: 18px;
            @include transition(.4s);
            @include transform(translateY(20px));
        }
    }
}
.single-gallery-item-two{
    & .img-holder{
        & img{
            @include border-radius(12px);
        }
    }
    & .content{
        padding: 33px 40px 0;
        & h3.title{
            margin-bottom: 5px;
        }
    }
}