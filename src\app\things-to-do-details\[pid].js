import { use } from 'react';
import ThingsToDoDetailsPage from "../../components/ThingsToDoDetails/ThingsToDoDetailsPage";
import thingsToDo from '../../data/thingsToDo';
import { ActivitySchema } from '../../components/SEO/JsonLdSchema';
import EnhancedSEO from '../../components/SEO/EnhancedSEO';

// Generate metadata for things-to-do pages
export async function generateMetadata({ params }) {
  const { pid } = params;
  const activity = thingsToDo.find((item) => item.id === parseInt(pid));

  if (!activity) {
    return {
      title: 'Atividade não encontrada | Descubra Cabo Verde',
      description: 'A atividade solicitada não foi encontrada.'
    };
  }

  return {
    title: `${activity.title} | Coisas para Fazer em Cabo Verde`,
    description: activity.description || `Descubra ${activity.title} em ${activity.location}. ${activity.category} em Cabo Verde.`,
    openGraph: {
      title: `${activity.title} | Coisas para Fazer em Cabo Verde`,
      description: activity.description || `Descubra ${activity.title} em ${activity.location}.`,
      url: `https://www.guiadecaboverde.cv/things-to-do-details/${pid}`,
      images: activity.image ? [`https://www.guiadecaboverde.cv${activity.image}`] : [],
    },
  };
}

const DestinationDetails = ({ params }) => {
  const { pid } = use(params);

  if (!pid) return <div>Loading...</div>;

  const activity = thingsToDo.find((item) => item.id === parseInt(pid));
  if (!activity) {
    return <div>Atividade não encontrada</div>;
  }

  const currentUrl = `https://www.guiadecaboverde.cv/things-to-do-details/${pid}`;
  const breadcrumbs = [
    { name: 'Início', url: '/' },
    { name: 'Coisas para Fazer', url: '/things-to-do' },
    { name: activity.title, url: `/things-to-do-details/${pid}` }
  ];

  return (
    <>
      <EnhancedSEO
        title={`${activity.title} | Coisas para Fazer em Cabo Verde`}
        description={activity.description || `Descubra ${activity.title} em ${activity.location}. ${activity.category} em Cabo Verde.`}
        url={currentUrl}
        image={activity.image}
        breadcrumbs={breadcrumbs}
      />
      <ActivitySchema activity={activity} />
      <ThingsToDoDetailsPage pid={pid} />
    </>
  );
};

export default DestinationDetails;