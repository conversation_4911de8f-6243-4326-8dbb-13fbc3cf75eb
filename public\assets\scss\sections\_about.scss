
/*---==================
    04. About css 
=================----*/

.about-two_content-box{
    & > p{
        margin-bottom: 45px;
    }
    & .card-list{
        max-width: 500px;
        display: flex;
        flex-wrap: wrap;
        & .card-item{
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 14px 20px;
            box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.15);
            @extend %white-bg;
            @include border-radius(10px);
            margin-bottom: 15px;
            font: 500 18px $font;
            margin-right: 18px;
            @media #{$lg}{
                margin-right: 10px;
            }
            & i{
                @extend %secondary-color;
                margin-right: 15px;
                font-size: 28px;
            }
            &:nth-child(1){
                @include transform(rotate(-7.59deg));
                @media #{$xs}{
                    @include transform(rotate(0deg));
                }
            }
            &:nth-child(2){
                @include transform(rotate(2.25deg));
                @media #{$xs}{
                    @include transform(rotate(0deg));
                }
            }
            &:nth-child(3){
                @include transform(rotate(-8.38deg));
                margin-left: 30px;
                @media #{$xs}{
                    margin-left: 0;
                    @include transform(rotate(0deg));
                }
            }
            &:nth-child(4){
                @include transform(rotate(1.92deg));
                @media #{$xs}{
                    @include transform(rotate(0deg));
                }
            }
            &:nth-child(5){
                @include transform(rotate(-8.06deg));
                @media #{$xs}{
                    @include transform(rotate(0deg));
                }
            }
            &:nth-child(6){
                @include transform(rotate(3.34deg));
                @media #{$xs}{
                    @include transform(rotate(0deg));
                }
            }
        }
    }
}