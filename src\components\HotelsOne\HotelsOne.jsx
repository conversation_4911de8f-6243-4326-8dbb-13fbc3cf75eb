import destinationsOne from '../../data/destinationsOne'
import SingleHotel from './SingleHotel'

const DestinationsOne = () => {
  return (
    <section className="team-section pt-100 pb-70">
      
        <div className='section-title text-center'>
          <span className='section-title__tagline'>Destination lists</span>
          <h2 className='section-title__title'>Beautiful Places In Kenya</h2>
        </div>
        <div className='row'>
          {destinationsOne.slice(0, 5).map((destination) => (
            <SingleHotel key={destination.id} destination={destination} />
          ))}
        </div>
    </section>
  )
}

export default DestinationsOne
