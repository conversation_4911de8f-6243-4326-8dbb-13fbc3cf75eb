'use client'

import useWindowSize from "../../useWindowSize";
import { stickyNav } from "../../utils/utils";
import { useEffect } from "react";
import Header2 from "./Header2";
const Header = ({ header }) => {
  useEffect(() => {
    stickyNav();
  }, []);
  const { width } = useWindowSize();
  useEffect(() => {
    const headers = document.querySelectorAll(".gowilds-header");
    headers.forEach((header) => {
      if (width >= 1199) {
        header.classList.remove("mobile-header");
      } else {
        header.classList.add("mobile-header");
      }
    });
  }, [width]);

  switch (header) {
    case 1:
      return <Header2 />;
    case 2:
      return <Header2 />;

    default:
      return <Header2 />;
  }
};

export default Header;
