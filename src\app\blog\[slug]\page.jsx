'use client';
import { use } from 'react';
import { useRouter } from 'next/navigation';
import NewsBanner from "../../../components/NewsBanner";
import Layout from "../../../layout/Layout";
import Link from "next/link";
import Image from "next/image";
import blogData from '../../../data/blogData';
import { ArticleSchema } from '../../../components/SEO/JsonLdSchema';
import EnhancedSEO from '../../../components/SEO/EnhancedSEO';

const BlogDetails = ({ params }) => {
  const { slug } = use(params);

  // Encontre o post do blog que corresponde ao slug
  const post = blogData.find(blog => blog.slug === slug);

  if (!post) {
    return <div>Carregando...</div>;  // Lidar com o caso em que o post não é encontrado ou os dados ainda estão carregando
  }

  const currentUrl = `https://www.guiadecaboverde.cv/blog/${slug}`;
  const breadcrumbs = [
    { name: '<PERSON><PERSON><PERSON>', url: '/' },
    { name: 'Blog', url: '/blog' },
    { name: post.title, url: `/blog/${slug}` }
  ];

  return (
    <Layout header={2}>
      <EnhancedSEO
        title={`${post.title} | Blog Cabo Verde`}
        description={post.content?.introduction || post.description || `Leia sobre ${post.title} no nosso blog de viagem sobre Cabo Verde.`}
        url={currentUrl}
        image={post.banner?.image}
        type="article"
        article={{
          author: post.meta?.author,
          publishedTime: post.meta?.date ? new Date(post.meta.date).toISOString() : new Date().toISOString(),
          modifiedTime: post.meta?.dateModified ? new Date(post.meta.dateModified).toISOString() : new Date().toISOString(),
          tags: post.sidebar?.tags || ['Cabo Verde', 'Viagem', 'Turismo']
        }}
        breadcrumbs={breadcrumbs}
      />
      <ArticleSchema article={post} url={currentUrl} />
      <NewsBanner pageTitle={post.title} blogImage={post.banner.image}/>
      {/*====== Início da Seção de Detalhes do Blog ======*/}
      <section className="blog-details-section pt-100 pb-70">
        <div className="container">
          <div className="row">
            <div className="col-xl-8">
              {/*=== Wrapper de Detalhes do Blog ===*/}
              <div className="blog-details-wrapper pr-lg-50">
                <div className="blog-post mb-60 wow fadeInUp">
                  <div className="post-thumbnail">
                    <Image
                      width={640}
                      height={343}
                      src={post.banner.image}
                      alt={post.banner.alt}
                      style={{objectFit: "cover"}}
                    />
                  </div>
                  <div className="post-meta text-center pt-25 pb-15 mb-25">
                    <span>
                      <i className="far fa-user" />
                      <Link href="#">{post.meta.author}</Link>
                    </span>
                    <span>
                      <i className="far fa-calendar-alt" />
                      <Link href="#">{post.meta.date}</Link>
                    </span>
                    <span>
                      <i className="far fa-comment" />
                      <Link href="#">{`Comentários (${post.meta.comments})`}</Link>
                    </span>
                  </div>
                  <div className="main-post">
                    <div className="entry-content">
                      <h3 className="title">{post.content.headline}</h3>
                      <p>{post.content.introduction}</p>
                      {post.content.sections.map((section, index) => (
                        <div key={index}>
                          <h4>{section.title}</h4>
                          <p>{section.content}</p>
                        </div>
                      ))}
                      <blockquote className="block-quote pt-100">
                        <Image
                          width={50}
                          height={35}
                          src={post.content.quote.image}
                          alt="imagem de citação"
                        />
                        <h3>{post.content.quote.text}</h3>
                        <span>{post.content.quote.author}</span>
                      </blockquote>
                    </div>
                  </div>
                </div>
                {/*===  Seção de Comentários (Você pode manter isso estático ou torná-lo dinâmico) ===*/}
              </div>
            </div>

            <div className="col-xl-4">
              <div className="sidebar-widget-area">

                {/*=== Widget de Posts Recentes ===*/}
                <div className="sidebar-widget recent-post-widget mb-40 wow fadeInUp">
                  <h4 className="widget-title">Notícias Recentes</h4>
                  <ul className="recent-post-list">
                    {post.sidebar.recentPosts.map((recentPost, index) => (
                      <li key={index} className="post-thumbnail-content">
                        <Image
                          width={130}
                          height={130}
                          src={recentPost.image}
                          alt="miniatura do post"
                        />
                        <div className="post-title-date">
                          <h5>
                            <Link href={recentPost.link}>{recentPost.title}</Link>
                          </h5>
                          <span className="posted-on">
                            <i className="far fa-calendar-alt" />
                            <Link href="#">{recentPost.date}</Link>
                          </span>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>

                {/*=== Widget de Banner ===*/}
                <div className="sidebar-widget sidebar-banner-widget wow fadeInUp mb-40">
                  <div className="banner-widget-content">
                    <div className="banner-img">
                      <Image
                        width={410}
                        height={290}
                        src={post.sidebar.banner.image}
                        alt="Banner do Post"
                      />
                      <div className="hover-overlay">
                        <div className="hover-content">
                          <h4 className="title">
                            <Link href="#">{post.sidebar.banner.title}</Link>
                          </h4>
                          <p>
                            <i className="fas fa-map-marker-alt" />
                            {post.sidebar.banner.location}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/*=== Widget de Tags ===*/}
                <div className="sidebar-widget tag-cloud-widget gray-bg mb-40 wow fadeInUp">
                  <h4 className="widget-title">Tags do Blog</h4>
                  {post.sidebar.tags.map((tag, index) => (
                    <Link disabled aria-disabled={true} key={index} href="#"
                    style={{pointerEvents:"none"}}>
                      {tag}
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/*====== Fim da Seção de Detalhes do Blog ======*/}
    </Layout>
  );
};

export default BlogDetails;
