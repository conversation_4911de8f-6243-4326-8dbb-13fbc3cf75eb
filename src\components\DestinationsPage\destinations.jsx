'use client';
import React from "react";
import destinationDetails from "../../data/destinationDetails";
import Masonry from "react-masonry-css";
import { Card, Button } from "react-bootstrap";
import { useRouter } from "next/navigation";
import styles from "./DestinationsPage.module.css";

const DestinationsPage = () => {
    const router = useRouter();

    const masonryBreakpoints = {
        default: 3,
        1100: 2,
        700: 1,
    };

    const handleReadMore = (id) => {
        router.push(`/destinations-details/${id}`);
    };

    return (
        <div className="container my-4">
            <Masonry
                breakpointCols={masonryBreakpoints}
                className={styles.masonryGrid}
                columnClassName={styles.masonryGridColumn}
            >
                {destinationDetails.map((destination) => (
                    <Card key={destination.id} className={`mb-4 ${styles.cardStyle}`}>
                        <Card.Img
                            src={destination.image}
                            alt={destination.discoverTitle}
                            className={styles.cardImage}
                        />

                        <Card.Body>
                            <Card.Title>{destination.discoverTitle}</Card.Title>
                            <Button
                                style={{ backgroundColor: "#fa4836", border: "none" }}
                                onClick={() => handleReadMore(destination.id)}
                            >
                                Read More
                            </Button>
                        </Card.Body>
                    </Card>
                ))}
            </Masonry>
        </div>
    );
};

export default DestinationsPage;
