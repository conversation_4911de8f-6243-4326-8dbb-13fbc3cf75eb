import Link from "next/link";
import Menu from "../Menu";
import Image from "next/image";

const Header2 = () => {
  return (
    <header className="header-area header-one transparent-header">
      {/*====== Header Navigation ======*/}
      <div className="header-navigation navigation-white">
        <div className="nav-overlay" />
        <div className="container-fluid">
          <div className="primary-menu">
            {/*====== Site Branding ======*/}
            <div className="site-branding">
              <Link href="/">
                <div className="brand-logo">
                  <Image
                    width={140}
                    height={60}
                    src="/assets/images/logo/guia-turistico-logo.png"
                    alt="Guia Turístico - Descubra Cabo Verde"
                    priority
                    style={{
                      width: 'auto',
                      height: '60px',
                      maxWidth: '140px',
                      objectFit: 'contain'
                    }}
                  />
                </div>
              </Link>
            </div>
            {/*====== Nav Menu ======*/}
            <div className="nav-menu">
              {/*====== Site Branding ======*/}
              <div className="mobile-logo mb-30 d-block d-xl-none">
                <Link href="/">
                  <div className="brand-logo">
                    <Image
                      width={120}
                      height={50}
                      src="/assets/images/logo/guia-turistico-logo.png"
                      alt="Guia Turístico - Descubra Cabo Verde"
                      style={{
                        width: 'auto',
                        height: '50px',
                        maxWidth: '120px',
                        objectFit: 'contain'
                      }}
                    />
                  </div>
                </Link>
              </div>
              {/*=== Nav Search ===*/}
              <div className="nav-search mb-30 d-block d-xl-none ">
                <form>
                  <div className="form_group">
                    <input
                      type="email"
                      className="form_control"
                      placeholder="Pesquisar Aqui"
                      name="email"
                      required
                    />
                    <button className="search-btn">
                      <i className="fas fa-search" />
                    </button>
                  </div>
                </form>
              </div>
              {/*====== main Menu ======*/}
              <Menu />
              {/*====== Menu Button ======*/}
              <div className="menu-button mt-40 d-xl-none">
                <Link href="/contact">
                  <div className="main-btn secondary-btn">
                    Contactar
                    <i className="far fa-paper-plane">

                    </i>
                  </div>
                </Link>
              </div>
            </div>
            {/*====== Nav Right Item ======*/}
            <div className="nav-right-item">
              <div className="menu-button d-xl-block d-none">
                <Link href="/contact">
                  <div className="main-btn primary-btn">
                    Contactar
                    <i className="far fa-paper-plane">

                    </i>
                  </div>
                </Link>
              </div>
              <div className="navbar-toggler">
                <span />
                <span />
                <span />
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};
export default Header2;
