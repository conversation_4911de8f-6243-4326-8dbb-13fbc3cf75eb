
/*---==================
    05. Elements css 
=================----*/

/* Features Item */ 

.single-features-item{
    &:hover{
        & .img-holder{
            & .content{
                & p{
                    opacity: 1;
                    max-height: 90px;
                    margin-top: 20px;
                }
            }
        }
    }
    & .img-holder{
        position: relative;
        & img{
            width: 100%;
            @include border-radius(7px);
        }
        & .content{
            @extend %white-bg;
            position: absolute;
            bottom: 30px;
            left: 30px;
            right: 30px;
            padding: 35px 25px 25px;
            box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.1);
            @include border-radius(10px);
            @include transition(.3s);
            @media #{$lp}{
                padding: 35px 25px 25px;
            }
        }
        & .text{
            display: flex;
            align-items: center;
            justify-content: space-between;
            & h4.title{
                flex: 0 0 auto;
                width: 70%;
                &:before{
                    display: block;
                    content: '';
                    width: 45px;
                    height: 3px;
                    @extend %primary-bg;
                    margin-bottom: 15px;
                    @include border-radius(10px);
                }
                @media #{$lm}{
                    font-size: 16px;
                }
            }
            & .icon-btn{
                flex-grow: 1;
                flex: 0 0 auto;
                width: 45px;
                height: 45px;
                @include border-radius(50%);
                @extend %primary-bg;
                @extend %white-color;
                @extend %flex-center;
                @media #{$xss}{
                    width: 40px;
                    height: 40px;
                    font-size: 13px;
                }
            }
        }
        & p{
            height: auto;
            max-height: 0;
            opacity: 0;
            @include transition(.4s);
            @media #{$lm}{
                font-size: 14px;
            }
        }
    }
}

.single-features-item-two{
    &:hover{
        & .img-holder{
            & img{
                @include transform(scale(1.1));
            }
        }
    }
    & .img-holder{
        position: relative;
        overflow: hidden;
        @include border-radius(20px);
        & img{
            width: 100%;
            @include transform(scale(1));
            @include transition(.3s);
        }
    }
    & .item-overlay{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        padding: 40px 30px 35px;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 43.14%, rgba(0, 0, 0, 0.8) 100%);
        display: flex;
        align-items: flex-end;
        justify-content: center;
        
    }
    & .content{
        & h3.title{
            font-weight: 500;
            @extend %white-color;
        }
    }
}

.single-features-item-three{
    &:hover{
        & .content{
            @extend %primary-bg;
            & h6{
                @extend %white-color;
            }
        }
    }
    & .img-holder{
        img{
            width: 100%;
            border-radius: 7px 7px 0 0;
        }
    }
    & .content{
        padding: 20px 25px;
        @extend %white-bg;
        border-radius: 0 0 7px 7px;
        @include transition(.3s);
        & h6{
            font: 500 16px $font;
            line-height: 21px;
        }
    }
}


.single-features-list{
    display: flex;
    @media #{$xss}{
        align-items: flex-start;
    }
    & .icon-inner{
        & .icon-check{
            line-height: 1;
            & i{
                @extend %secondary-color;
                font-size: 24px;
            }
        }
        & .icon{
            position: relative;
            margin-left: 55px;
            margin-right: 35px;
            flex: 0 0 auto;
            width: 80px;
            height: 80px;
            @include border-radius(50%);
            @extend %white-bg;
            @extend %primary-color;
            font-size: 40px;
            box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.1);
            @extend %flex-center;
            z-index: 1;
            @media #{$xss}{
                margin-left: 20px;
                margin-right: 15px;
            }
            &:after{
                position: absolute;
                content: '';
                width: 2px;
                height: 100%;
                border: 1px dashed #ccc;
                top: 100%;
                z-index: -1;
            }
        }
    }
    & .content{
        & h4{
            margin-bottom: 5px;
            @media #{$xss}{
                font-size: 20px;
            }
        }
        & p{
            line-height: 25.6px;
        }
    }
}

/* Fancy Icon Box */ 

.fancy-icon-box{
    padding: 40px 30px 32px;
    display: flex;
    @extend %gray-bg;
    border: 1px solid transparent;
    @include border-radius(7px);
    @include transition(.3s);
    @media #{$xss}{
        padding: 40px 20px 32px; 
    }
    &:hover{
        box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.07);
        border-color: $primary-color;
        & .icon{
            @extend %primary-bg;
            @extend %white-color;
        }
    }
    & .icon{
        flex: 0 0 auto;
        width: 75px;
        height: 75px;
        @include border-radius(50%);
        @extend %flex-center;
        font-size: 35px;
        @extend %white-bg;
        line-height: 1;
        margin-right: 20px;
        @include transition(.4s);
        @media #{$xss}{
            width: 55px;
            height: 55px;
            font-size: 24px;
            margin-right: 15px;
        }
    }
    & .text{
        & h4.title{
            margin-bottom: 6px;
            @media #{$lp}{
                font-size: 20px;
            }
        }
    }
}


.fancy-icon-box-two{
    @extend %white-bg;
    padding: 40px 30px 30px;
    box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.1);
    @include border-radius(10px);
    @include transition(.3s);
    &:hover{
        & .icon{
            @extend %primary-bg;
            @extend %white-color;
        }
    }
    & .icon{
        width: 100px;
        height: 100px;
        @include border-radius(50%);
        @extend %flex-center;
        display: inline-flex;
        @extend %gray-bg;
        margin-bottom: 28px;
        font-size: 50px;
        @extend %primary-color;
        @include transition(.4s);
    }
    & .text{
        & h3.title{
            margin-bottom: 10px;
        }
    }
}

.fancy-icon-box-three{
    @extend %gray-bg;
    @include border-radius(65px);
    display: flex;
    align-items: center;
    padding: 10px 50px 10px 10px;
    @include transition(.3s);
    &:hover{
        @extend %primary-bg;
        & .text{
            & h5.title{
                @extend %white-color;
                border-color: #fff;
            }
            & .btn-link{
                @extend %white-color;
            }
        }
    }
    & .icon{
        flex: 0 0 auto;
        width: 110px;
        height: 110px;
        @include border-radius(50%);
        @extend %white-bg;
        font-size: 60px;
        @extend %flex-center;
        @extend %primary-color;
        margin-right: 30px;
        @media #{$xs}{
            margin-right: 20px;
        }
    }
    & .text{
        & h5.title{
            font-weight: 500;
            padding-bottom: 13px;
            border-bottom: 1px solid #1D231F;
            margin-bottom: 7px;
        }
        & .btn-link{
            text-decoration: underline;
        }
    }
}

.fancy-icon-box-four{
    display: flex;
    & .icon{
        flex: 0 0 auto;
        width: 35px;
        margin-right: 20px;
        & i{
            font-size: 35px;
            @extend %primary-color;
        }
    }
    & .text{
        margin-top: -7px;
        & h4.title{
            margin-bottom: 5px;
            font-weight: 500;
        }
        & p{
            line-height: 28px;
        }
    }
}

/* Single Team Item */

.single-team-item{
    @extend %white-bg;
    @include border-radius(7px);
    box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.07);
    padding: 35px 30px 30px;
    display: flex;
    & .member-img{
        width: 90px;
        flex: 0 0 auto;
        margin-right: 20px;
        & img{
            width: 100%;
            @include border-radius(50%);
        }
    }
    & .member-info{
        & ul.social-link{
            padding-top: 20px;
            border-top: 1px solid rgba(29, 35, 31, 0.1);
            margin-top: 20px;
            line-height: 1;
            & li{
                &:not(:last-child){
                    margin-right: 15px;
                }
                & a{
                    &:hover{
                        @extend %primary-color;
                    }
                }
            }
        }
        & h4.title{
            @media #{$lp}{
                font-size: 19px;
            }
            @media #{$xm}{
                font-size: 17px;
            }
        }
        & p.position{
            @media #{$xm}{
                font-size: 14px;
            }
        }
    }
}

/* Single Counter Item */

.counter-item{
    @media #{$xs}{
        text-align: center;
    }
    & h2.number{
        @extend %primary-color;
        font: 500 55px $font;
        line-height: 1;
        margin-bottom: 5px;
    }
    & p{
        font: 400 16px $font;
    }
}

.single-counter-item{
    padding: 40px 30px 27px;
    @extend %white-bg;
    box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.05);
    border-radius: 7px;
    border: 1px solid transparent;
    @include transition(.3s);
    &:hover{
        border-color: #000;
        & .icon{
            @extend %primary-bg;
            @extend %white-color;
        }
    }
    & .icon{
        width: 145px;
        height: 145px;
        @include border-radius(50%);
        @extend %flex-center;
        background-color: rgba(99, 171, 69, 0.1);
        font-size: 70px;
        display: inline-flex;
        @extend %primary-color;
        margin-bottom: 35px;
        @include transition(.4s);
    }
    & h2.number{
        font: 500 45px $font;
        line-height: 55px;
    }
}

.single-counter-item-two{
    display: flex;
    justify-content: center;
    & .icon{
        @extend %white-bg;
        width: 95px;
        height: 95px;
        @include border-radius(50%);
        margin-bottom: 25px;
        @extend %flex-center;
        @extend %primary-color;
        font-size: 50px;
    }
    & .content{
        & h2.number{
            font: 500 55px $font;
            line-height: 1;
            margin-bottom: 5px;
        }
        & p{
            font-weight: 400;
        }
    }
}

/* Single Activity Item */

.single-activity-item{
    & .img-holder{
        & img{
            width: 100%;
            border-radius: 20px 20px 0px 0px;
        }
    }
    & .content{
        @extend %gray-bg;
        border-radius: 0px 0px 20px 20px;
        padding: 0 40px 40px;
        & .meta{
            position: relative;
            @extend %white-bg;
            box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.07);
            @include border-radius(10px);
            margin: -35px 0 35px;
            display: flex;
            padding: 21px 40px;
            @media #{$xm}{
                padding: 21px 30px;
            }
            & ul{
                display: flex;
                align-items: center;
                flex-grow: 1;
                line-height: 0;
                & li{
                    display: inline-block;
                    &:not(:last-child){
                        margin-right: 30px;
                        @media #{$xm}{
                            margin-right: 20px;
                        }
                    }
                    & span{
                        font-size: 20px;
                        @media #{$xm}{
                            font-size: 16px;
                        }
                    }
                }
            }
            & .rate{
                display: flex;
                align-items: center;
                font: 500 16px $font;
                color: #000;
                & i{
                    @extend %secondary-color;
                    margin-right: 5px;
                }
            }
        }
        & h3.title{
            margin-bottom: 9px;
        }
        & p{
            margin-bottom: 20px;
        }
        & .main-btn{
            & i{
                @extend %black-bg;
                @extend %white-color;
            }
            &:hover{
                & i{
                    @extend %primary-color;
                }
            }
        }
    }
}

/* Single Event Item */

.single-event-item{
    padding: 15px;
    box-shadow: 0px 7px 60px rgba(0, 0, 0, 0.07);
    @extend %white-bg;
    @include border-radius(12px);
    & .img-holder{
        & img{
            width: 100%;
            @include border-radius(12px);
        }
    }
    & .content{
        padding: 30px 35px 10px;
        & .meta{
            & span{
                margin-bottom: 10px;
                font-weight: 400;
                text-transform: uppercase;
                & i{
                    margin-right: 5px;
                    @extend %primary-color;
                }
            }
        }
        & h3.title{
            margin-bottom: 25px;
            padding-bottom: 27px;
            font-weight: 500;
            border-bottom: 1px solid rgba(29, 35, 31, 0.1);
            @media #{$xm}{
                font-size: 18px;
                line-height: 30px;
            }
            &:hover{
                @extend %primary-color;
            }
        }
        & p.location{
            font-size: 18px;
            & i{
                @extend %secondary-color;
            }
            @media #{$xm}{
                font-size: 14px;
            }
        }
    }
}

/* Single CTA Item */ 

.single-cta-item{
    display: flex;
    align-items: center;
    &:nth-child(2){
        & .content{
            & .icon-btn{
                @extend %secondary-bg;
            }
        }
    }
    & .icon{
        flex: 0 0 auto;
        width: 85px;
        margin-right: 30px;
    }
    & .content{
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        & h3.title{
            margin-right: 100px;
            line-height: 1.5;
            @media #{$lp}{
                margin-right: 40px;
            }
            @media #{$lm}{
                font-size: 22px;
                margin-right: 30px;
            }
            @media #{$xss}{
                font-size: 18px;
                line-height: 30px;
            }
        }
        & .icon-btn{
            flex: 0 0 auto;
            width: 65px;
            height: 65px;
            @include border-radius(50%);
            @extend %primary-bg;
            @extend %flex-center;
            @extend %white-color;
            box-shadow: 0px 10px 60px rgba(99, 171, 69, 0.65);
            & i{
                font-size: 27px;
                @include transform(rotate(-38.07deg));
                margin-top: 5px;
            }
            @media #{$xss}{
                display: none;
            }
        }
    }
}

/* Single Info Item */ 

.single-info-item{
    display: flex;
    & .icon{
        flex: 0 0 auto;
        width: 50px;
        height: 50px;
        @include border-radius(50%);
        font-size: 18px;
        @extend %primary-bg;
        @extend %flex-center;
        margin-right: 15px;
        @extend %white-color;
    }
    & .info{
        & span.title{
            font: 500 14px $font;
            line-height: 26px;
        }
        & p{
            line-height: 21px;
        }
    }
}

.single-info-item-two{
    display: flex;
    & .inner-info{
        display: flex;
    }
    & .icon{
        flex: 0 0 auto;
        width: 50px;
        height: 50px;
        @include border-radius(50%);
        @extend %flex-center;
        border: 1px solid rgba(29, 35, 31, 0.1);
        margin-right: 20px;
        margin-left: 20px;
        @extend %primary-color;
    }
    & .info{
        & span.title{
            font-weight: 400;
            line-height: 20px;
            @media #{$lg}{
                font-size: 12px;
            }
        }
        & h5{
            line-height: 1;
            @media #{$lp}{
                font-size: $h5-font;
            }
            @media #{$lg}{
                font-size: $h5-font;
            }
            &:hover{
                & a{
                    @extend %primary-color;
                }
            }
        }
    }
}

/* Progress Bar */ 

.single-progress-bar{
    & .progress-title{
        & h6{
            margin-bottom: 10px;
            & span{
                float: right;
            }
        }
    }
    & .progress{
        height: 5px;
        border-radius: 0;
        background-color: rgba(29, 35, 31, 0.1);
        & .progress-bar{
            @extend %secondary-bg;
        }
    }
}

.single-skill-circle{
    position: relative;
    padding: 0 40px;
    @media #{$lm}{
        padding: 0 30px;
    }
    &:not(:last-child){
        @media #{$xss}{
            margin-bottom: 30px;
        }
        &:after{
            position: absolute;
            right: 0;
            top: 0;
            content: '';
            width: 1px;
            height: 100%;
            background-color: #D9D9D9;
            @media #{$xss}{
                display: none;
            }
        }
    }
    & .inner-circle{
        position: relative;
        width: 120px;
        height: 120px;
        border-radius: 50%;
        @extend %flex-center;
        display: inline-flex;
        margin-bottom: 20px;
        overflow: hidden;
        & .number{
            font: 500 27px $font;
            @extend %heading;
        }
        & .line{
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 10px solid $primary-color;
            border-color: transparent $primary-color $primary-color;
            @include transform(rotate(55deg));
        }
    }
    & h5{
        @media #{$lg}{
            font-size: 18px;
        }
    }
}


/* Partners Item */ 


.single-partner-item{
    & .partner-img{
        padding: 0 20px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/*--==== Pagination Css ====--*/

.tdk-pagination{
    & li{
        display: inline-block;
        & a{
            width: 55px;
            height: 55px;
            @include border-radius(50%);
            border: 2px solid rgba(29, 35, 31, 0.1);
            font: 500 16px $font;
            @extend %flex-center;
            margin-bottom: 10px;
            @media #{$xs}{
                width: 40px;
                height: 40px;
                font-size: 14px;
            }
            &.active,
            &:hover{
                @extend %primary-bg;
                border-color: transparent;
                @extend %white-color;
            }
        }
    }
}