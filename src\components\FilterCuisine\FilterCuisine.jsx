'use client';
import { useState, useEffect } from "react";
import SingleService from "../SingleService/SingleService";
import { Container, Row, Col } from "react-bootstrap";
import styles from "./CuissineFilter.module.css";
import Pagination from "../Pagination/Pagination";

const RestaurantsDisplay = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const restaurantsPerPage = 10;

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      const payload = {
        searchParameters: {
          LINGUA_ID: 1,
          PESQUISA_F: "Restaurantes",
          LOCALITY_F: "",
          TITULO_ID: 452,
          country: "CBV",
        },
      };

      try {
        const response = await fetch(
          "https://api.yellowpageskenya.com/v1/search",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(payload),
          }
        );

        if (!response.ok) {
          throw new Error("Network response was not ok");
        }

        const result = await response.json();

        // Adjust based on the API response structure:
        const fetchedData = result.data || result.results || [];
        setData(fetchedData);
      } catch (err) {
        setError(err.message || "Something went wrong");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Use the 'data' state for pagination
  const indexOfLastRestaurant = currentPage * restaurantsPerPage;
  const indexOfFirstRestaurant = indexOfLastRestaurant - restaurantsPerPage;
  const currentRestaurants = data.slice(
    indexOfFirstRestaurant,
    indexOfLastRestaurant
  );

  const onPageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  return (
    <Container className={`py-4 ${styles.container}`}>
      <Row className="justify-content-center mb-4">
        <Col md={8}>
          <h1 className={`text-center mb-3 ${styles.heading}`}>
            All Restaurants
          </h1>
        </Col>
      </Row>

      {loading && (
        <Row className="justify-content-center">
          <Col md={12} className="text-center">
            <p>Loading restaurants...</p>
          </Col>
        </Row>
      )}
      {error && (
        <Row className="justify-content-center">
          <Col md={12} className="text-center text-danger">
            <p>Error: {error}</p>
          </Col>
        </Row>
      )}

      <Row>
        {currentRestaurants && currentRestaurants.length > 0 ? (
          currentRestaurants.map((item) => (
            <Col key={item.nome_loc_tit_id} md={4} className="mb-4">
              <SingleService
                service={item}
                key={item.nome_loc_tit_id}
                url="https://www.yellowpageskenya.com/business-category/services/business-details"
              />
            </Col>
          ))
        ) : (
          !loading && (
            <Col md={12} className="text-center">
              <p>No restaurants found.</p>
            </Col>
          )
        )}
      </Row>
      <Pagination
        currentPage={currentPage}
        totalPages={Math.ceil(data.length / restaurantsPerPage)}
        onPageChange={onPageChange}
      />
    </Container>
  );
};

export default RestaurantsDisplay;
