import Head from 'next/head';
import { NextSeo } from 'next-seo';

const SEO = ({ title, description, url, image }) => {
  return (
    <div>
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href={url} />
      </Head>
      <NextSeo
        title={title}
        description={description}
        canonical={url}
        openGraph={{
          url: url,
          title: title,
          description: description,
          images: [
            {
              url: image || '/default-image.jpg',
              width: 1200,
              height: 630,
              alt: title,
            },
          ],
          site_name: 'Travel Discover Cape Verde',
        }}
        twitter={{
          cardType: 'summary_large_image',
          site: '@traveldiscovercapeverde',
          title: title,
          description: description,
          image: image || '/default-image.jpg',
        }}
      />
    </div>
  );
};

export default SEO;
