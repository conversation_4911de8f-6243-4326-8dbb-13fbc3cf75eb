'use client'

import { useEffect } from 'react'
import Script from 'next/script'
import { pageview, event as trackEvent } from './gtag'
import WebVitals from './WebVitals'

const Analytics = () => {
  const Yandex_Metrica_ID = process.env.NEXT_PUBLIC_YANDEX_METRICA_ID
  const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS
  const Meta_Pixel_ID = process.env.NEXT_PUBLIC_META_PIXEL_ID
  const GTM_ID = process.env.NEXT_PUBLIC_GTM_ID
  const isDevelopment =
    process.env.NODE_ENV === 'development' ||
    (typeof window !== 'undefined' && window.location.hostname === 'localhost')

  useEffect(() => {
    if (typeof window !== 'undefined' && !isDevelopment) {
      const trackPageView = () => {
        const path = window.location.pathname

        // Google Analytics pageview
        pageview(path)

        // GTM pageview
        if (window.dataLayer) {
          window.dataLayer.push({
            event: 'pageview',
            page: path
          })
        }

        // Yandex Metrica pageview
        if (window.ym) {
          window.ym(Yandex_Metrica_ID, 'hit', path, {
            title: document.title,
            referer: document.referrer
          })
        }
      }

      const trackSearchEvent = () => {
        const searchInput = document.querySelector('input[type="search"]')
        if (searchInput) {
          const searchTerm = searchInput.value
          const category =
            document.body.getAttribute('data-category') || 'default-category'

          trackEvent({
            action: 'search',
            category: 'User Interaction',
            label: searchTerm,
            value: searchTerm
          })

          // Yandex Metrica search event tracking
          if (window.ym) {
            window.ym(Yandex_Metrica_ID, 'reachGoal', 'search', {
              searchTerm,
              category
            })
          }
        }
      }

      // Track page view and search event on page load
      trackPageView()
      trackSearchEvent()

      // Track events with GTM
      const trackGTMEvent = (eventName, eventData) => {
        if (window.dataLayer) {
          window.dataLayer.push({
            event: eventName,
            ...eventData
          })
        }
      }

      // Enhanced event tracking function
      const trackClickEvent = (category, label, value = null) => {
        // GA tracking
        trackEvent({
          action: 'click',
          category,
          label,
          value
        })

        // GTM tracking
        trackGTMEvent('click_event', {
          category,
          label,
          value
        })

        // Yandex tracking
        if (window.ym) {
          window.ym(Yandex_Metrica_ID, 'reachGoal', 'click', {
            category,
            label,
            value
          })
        }

        // Meta Pixel tracking for conversion events
        if (window.fbq && (category === 'Contact' || category === 'Lead Generation')) {
          window.fbq('track', 'Lead', { content_name: label })
        }
      }

      // Enhanced form submission tracking
      const trackFormSubmission = (formName, formData = {}) => {
        // GA tracking
        trackEvent({
          action: 'submit',
          category: 'Form Submission',
          label: formName
        })

        // GTM tracking
        trackGTMEvent('form_submission', {
          form_name: formName,
          ...formData
        })

        // Yandex tracking
        if (window.ym) {
          window.ym(Yandex_Metrica_ID, 'reachGoal', 'form_submit', {
            formName,
            ...formData
          })
        }

        // Meta Pixel conversion tracking
        if (window.fbq) {
          window.fbq('track', 'Lead', { content_name: formName })
        }
      }

      // New: Business contact event tracking
      const trackBusinessContact = (contactType, businessInfo = {}) => {
        const eventData = {
          action: 'contact',
          category: 'Business Contact',
          label: contactType,
          value: businessInfo.businessName || 'Unknown'
        }

        // GA tracking
        trackEvent(eventData)

        // GTM tracking
        trackGTMEvent('business_contact', {
          contact_type: contactType,
          business_name: businessInfo.businessName,
          business_category: businessInfo.category
        })

        // Yandex tracking
        if (window.ym) {
          window.ym(Yandex_Metrica_ID, 'reachGoal', 'business_contact', {
            contactType,
            ...businessInfo
          })
        }

        // Meta Pixel conversion tracking
        if (window.fbq) {
          window.fbq('track', 'Contact', {
            content_name: contactType,
            content_category: businessInfo.category
          })
        }
      }

      // New: CTA and engagement tracking
      const trackEngagement = (engagementType, details = {}) => {
        const eventData = {
          action: engagementType,
          category: 'User Engagement',
          label: details.label || engagementType,
          value: details.value
        }

        // GA tracking
        trackEvent(eventData)

        // GTM tracking
        trackGTMEvent('user_engagement', {
          engagement_type: engagementType,
          ...details
        })

        // Yandex tracking
        if (window.ym) {
          window.ym(Yandex_Metrica_ID, 'reachGoal', 'engagement', {
            engagementType,
            ...details
          })
        }
      }

      // New: Scroll depth tracking
      const trackScrollDepth = () => {
        let maxScroll = 0
        const milestones = [25, 50, 75, 90, 100]
        const trackedMilestones = new Set()

        const handleScroll = () => {
          const scrollTop = window.pageYOffset
          const docHeight = document.documentElement.scrollHeight - window.innerHeight
          const scrollPercent = Math.round((scrollTop / docHeight) * 100)

          if (scrollPercent > maxScroll) {
            maxScroll = scrollPercent

            milestones.forEach(milestone => {
              if (scrollPercent >= milestone && !trackedMilestones.has(milestone)) {
                trackedMilestones.add(milestone)

                trackEngagement('scroll_depth', {
                  label: `${milestone}%`,
                  value: milestone,
                  page: window.location.pathname
                })
              }
            })
          }
        }

        window.addEventListener('scroll', handleScroll, { passive: true })
        return () => window.removeEventListener('scroll', handleScroll)
      }

      // New: Time on page tracking
      const trackTimeOnPage = () => {
        const startTime = Date.now()
        const milestones = [30, 60, 120, 300] // 30s, 1m, 2m, 5m

        milestones.forEach(seconds => {
          setTimeout(() => {
            trackEngagement('time_on_page', {
              label: `${seconds}s`,
              value: seconds,
              page: window.location.pathname
            })
          }, seconds * 1000)
        })

        // Track time when user leaves
        const handleBeforeUnload = () => {
          const timeSpent = Math.round((Date.now() - startTime) / 1000)
          trackEngagement('session_duration', {
            label: `${timeSpent}s`,
            value: timeSpent,
            page: window.location.pathname
          })
        }

        window.addEventListener('beforeunload', handleBeforeUnload)
        return () => window.removeEventListener('beforeunload', handleBeforeUnload)
      }

      // Enhanced click handler
      const handleClick = (event) => {
        const { target } = event

        // Business contact tracking
        if (target.tagName === 'A') {
          const href = target.getAttribute('href')

          if (href?.startsWith('tel:')) {
            const phoneNumber = href.replace('tel:', '')
            const businessName = target.closest('[data-business-name]')?.getAttribute('data-business-name')
            const businessCategory = target.closest('[data-business-category]')?.getAttribute('data-business-category')

            trackBusinessContact('phone_click', {
              phoneNumber,
              businessName,
              category: businessCategory
            })
          } else if (href?.startsWith('mailto:')) {
            const email = href.replace('mailto:', '')
            const businessName = target.closest('[data-business-name]')?.getAttribute('data-business-name')
            const businessCategory = target.closest('[data-business-category]')?.getAttribute('data-business-category')

            trackBusinessContact('email_click', {
              email,
              businessName,
              category: businessCategory
            })
          } else if (href?.startsWith('http') && !href.includes(window.location.hostname)) {
            // External link tracking
            trackClickEvent('External Link', href)
          } else if (href?.startsWith('/')) {
            // Internal link tracking
            trackClickEvent('Internal Link', href)
          }
        }

        // CTA button tracking
        if (target.tagName === 'BUTTON' || target.closest('button')) {
          const button = target.tagName === 'BUTTON' ? target : target.closest('button')
          const buttonText = button.textContent?.trim()
          const buttonType = button.getAttribute('type')
          const formName = button.closest('form')?.getAttribute('name')

          if (buttonType !== 'submit') {
            trackEngagement('cta_click', {
              label: buttonText,
              button_type: buttonType,
              form_context: formName
            })
          }
        }

        // Directory link tracking
        if (target.closest('[data-directory-link]')) {
          const directoryName = target.closest('[data-directory-link]').getAttribute('data-directory-name')
          trackEngagement('directory_access', {
            label: directoryName,
            value: directoryName
          })
        }

        // Flipbook tracking
        if (target.closest('[data-flipbook]')) {
          const flipbookName = target.closest('[data-flipbook]').getAttribute('data-flipbook-name')
          trackEngagement('flipbook_view', {
            label: flipbookName,
            value: flipbookName
          })
        }
      }

      // Enhanced form submission handler
      const handleFormSubmission = (event) => {
        const form = event.target
        const formName = form.getAttribute('name') || 'unnamed_form'
        const formData = new FormData(form)
        const formObject = Object.fromEntries(formData.entries())

        trackFormSubmission(formName, {
          form_fields: Object.keys(formObject).length,
          has_marketing_consent: formObject.marketingConsent === 'true'
        })
      }

      // Add event listeners
      document.addEventListener('click', handleClick)
      document.addEventListener('submit', handleFormSubmission)

      // Initialize engagement tracking
      const scrollCleanup = trackScrollDepth()
      const timeCleanup = trackTimeOnPage()

      // Make tracking functions globally available
      window.trackBusinessContact = trackBusinessContact
      window.trackEngagement = trackEngagement
      window.trackClickEvent = trackClickEvent

      return () => {
        document.removeEventListener('click', handleClick)
        document.removeEventListener('submit', handleFormSubmission)
        scrollCleanup?.()
        timeCleanup?.()
      }
    }
  }, [GA_TRACKING_ID, Yandex_Metrica_ID, GTM_ID, isDevelopment])

  if (isDevelopment) {
    return null // Don't render any analytics scripts in development
  }

  return (
    <>
      {/* Google Tag Manager */}
      <Script
        id="gtm-script"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','${GTM_ID}');
          `
        }}
      />

      {/* GTM NoScript */}
      <noscript>
        <iframe
          src={`https://www.googletagmanager.com/ns.html?id=${GTM_ID}`}
          height="0"
          width="0"
          style={{ display: 'none', visibility: 'hidden' }}
        />
      </noscript>

      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`}
      />

      <Script
        id="gtag-init"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_TRACKING_ID}', {
              page_title: document.title,
              page_location: window.location.href,
              send_page_view: true,
              anonymize_ip: true,
              allow_google_signals: true,
              allow_ad_personalization_signals: true
            });
          `
        }}
      />

      {/* Yandex Metrica Initialization */}
      <Script
        id="yandex-metrica-init"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            (function(m,e,t,r,i,k,a){m[i]=m[i]||function(){(m[i].a=m[i].a||[]).push(arguments)};
            m[i].l=1*new Date();
            k=e.createElement(t),a=e.getElementsByTagName(t)[0],k.async=1,k.src=r,a.parentNode.insertBefore(k,a)})
            (window, document, "script", "https://mc.yandex.ru/metrika/tag.js", "ym");

            ym(${Yandex_Metrica_ID}, "init", {
                clickmap:true,
                trackLinks:true,
                accurateTrackBounce:true,
                webvisor:true,
                trackHash:true,
                ecommerce:"dataLayer"
            });
          `
        }}
      />

      {/* Meta Pixel Code */}
      <Script
        id="meta-pixel-init"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '${Meta_Pixel_ID}');
            fbq('track', 'PageView');
          `
        }}
      />

      {/* Track Web Vitals */}
      <WebVitals />

      {/* UseStyle AI */}
      {/* <Script src="https://p.usestyle.ai" strategy="afterInteractive" /> */}
    </>
  )
}

export default Analytics
