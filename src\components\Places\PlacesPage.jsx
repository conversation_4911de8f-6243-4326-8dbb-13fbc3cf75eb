'use client';
import React from "react";
import Slider from "react-slick";
import { sliderActive3ItemDot } from "../../sliderProps";
import Image from "next/image";
import Link from "next/link";
import Description from "../Description/Description";

const events = [
  {
    image: "/assets/images/place/festival-musica-baía-gatas.jpg",
    title: "Festival de Música da Baía das Gatas",
    location: "Praia da Baía das Gatas, São Vicente",
    price: "2500",
    date: "Sáb, Ago 15, 2024",
    link: "/tour-details",
  },
  {
    image: "/assets/images/place/carnaval-mindelo.jpg",
    title: "Carnaval de Mindelo 2024",
    location: "Mindelo, São Vicente",
    price: "1500",
    date: "Dom, Fev 25, 2024",
    link: "/tour-details",
  },
  {
    image: "/assets/images/place/festival-tabanka.jpg",
    title: "Festival de Tabanka",
    location: "Assomada, Santiago",
    price: "1000",
    date: "<PERSON>áb, Mai 18, 2024",
    link: "/tour-details",
  },
  {
    image: "/assets/images/place/kriol-jazz.jpg",
    title: "Kriol Jazz Festival",
    location: "Praia, Santiago",
    price: "3000",
    date: "Sex, Abr 12, 2024",
    link: "/tour-details",
    attendees: "50",
  },
  {
    image: "/assets/images/place/festa-santa-maria.jpg",
    title: "Festa de Santa Maria",
    location: "Santa Maria, Sal",
    price: "800",
    date: "Dom, Set 08, 2024",
    link: "/tour-details",
    attendees: "30",
  },
  {
    image: "/assets/images/place/morna-festival.jpg",
    title: "Festival Internacional da Morna",
    location: "Mindelo, São Vicente",
    price: "2000",
    date: "Sáb, Jul 20, 2024",
    link: "/tour-details",
  },
];

const title = "Lugares para Visitar e Eventos em Cabo Verde";
const description =
  "Cabo Verde é um centro de destinos vibrantes e eventos emocionantes que celebram a sua rica cultura, história e beleza natural. Desde as ruas animadas de Praia até às paisagens serenas de Santo Antão, há sempre um lugar para explorar. Assista a festivais mundialmente famosos, presencie eventos musicais emocionantes, ou mergulhe nas tradições locais através da arte, música e gastronomia. A energia dinâmica de Cabo Verde garante experiências inesquecíveis para todos.";
const features = [
  "Cidade Velha – Património Mundial UNESCO",
  "Vulcão do Fogo – Maravilha Natural",
  "Salinas de Pedra de Lume – Experiência Única",
  "Festival de Música da Baía das Gatas",
  "Carnaval de Mindelo – Celebração Cultural",
  "Festival de Gamboa – Música e Aventura",
  "Praias de Boa Vista – Tartarugas Marinhas",
  "Trilhas de Santo Antão – Caminhadas Épicas",
  "Festival Kriol Jazz – Praia",
  "Festivais de Arte e Música em Mindelo",
  "Pico da Antónia – Vista Panorâmica",
  "Mercado de Sucupira – Artesanato Local"
];

const PlacesPage = () => (
  <div>
    {/*====== Start Places Section ======*/}
    <section className="places-seciton pt-45 pb-100">
      <div className="container">
      <Description title={title} description={description} features={features} />;

        <Slider {...sliderActive3ItemDot} className="slider-active-3-item-dot wow fadeInUp">
          {events.map((event, index) => (
            <div key={index} className="single-place-item mb-60">
              <div className="place-img">
                <Image
                  width={600}
                  height={400}
                  src={event.image}
                  alt={`Imagem do evento ${event.title} em ${event.location}`}
                  title={event.title}
                  style={{ objectFit: 'contain' }}
                />
              </div>
              <div className="place-content">
                <div className="info">
                  <h4 className="title">
                    <Link  href={event.link}>
                      <div>{event.title}</div>
                    </Link>
                  </h4>
                  <p className="location">
                    <i className="fas fa-map-marker-alt" />
                    {event.location}
                  </p>
                  <p className="price">
                    <i className="fas fa-usd-circle" />
                    A partir de <span className="currency">CVE </span>{event.price}
                  </p>
                  <div className="meta">
                    <span>
                      <i className="far fa-calendar" />
                      {event.date}
                    </span>
                    {event.attendees && (
                      <span>
                        <i className="far fa-user" />
                        {event.attendees}
                      </span>
                    )}
                    <span>
                      <Link  href={event.link}>
                        <div>
                          Detalhes
                          <i className="far fa-long-arrow-right" />
                        </div>
                      </Link>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </Slider>
      </div>
    </section>
    {/*====== End Places Section ======*/}
  </div>
);

export default PlacesPage;
