import { use } from 'react';
import DestinationsDetailsPage from '../../components/DestinationsDetails/DestinationDetailsPage'
import destinationDetails from '../../data/destinationDetails'

const DestinationsDetails = ({ params }) => {
    const { pid } = use(params);

    const destination = destinationDetails.find((item) => item.id === parseInt(pid));
    if (!destination) {
      return <p>Destination not found</p>;
    }

  return (
      <DestinationsDetailsPage pid={pid} />
  )
}

export default DestinationsDetails