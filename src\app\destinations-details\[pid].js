import { use } from 'react';
import DestinationsDetailsPage from '../../components/DestinationsDetails/DestinationDetailsPage'
import destinationDetails from '../../data/destinationDetails'
import { TouristDestinationSchema } from '../../components/SEO/JsonLdSchema'

// Generate metadata for destination pages
export async function generateMetadata({ params }) {
  const { pid } = params;
  const destination = destinationDetails.find((item) => item.id === parseInt(pid));

  if (!destination) {
    return {
      title: 'Destino não encontrado | Descubra Cabo Verde',
      description: 'O destino solicitado não foi encontrado.'
    };
  }

  return {
    title: `${destination.title} | Destinos Cabo Verde`,
    description: destination.description || `Descubra ${destination.title} em Cabo Verde. Explore este destino incrível com o nosso guia completo.`,
    openGraph: {
      title: `${destination.title} | Destinos Cabo Verde`,
      description: destination.description || `Descubra ${destination.title} em Cabo Verde.`,
      url: `https://www.guiadecaboverde.cv/destinations-details/${pid}`,
      images: destination.image ? [`https://www.guiadecaboverde.cv${destination.image}`] : [],
    },
  };
}

const DestinationsDetails = ({ params }) => {
    const { pid } = use(params);

    const destination = destinationDetails.find((item) => item.id === parseInt(pid));
    if (!destination) {
      return <p>Destination not found</p>;
    }

  return (
      <>
        <TouristDestinationSchema destination={destination} />
        <DestinationsDetailsPage pid={pid} />
      </>
  )
}

export default DestinationsDetails