import HotelBanner from "../../components/HotelBanner";
import Layout from "../../layout/Layout";
import PlacesPage from "../../components/Places/PlacesPage";

export const metadata = {
  title: "Lugares | Descubra Cabo Verde",
  description: "Descubra os melhores lugares para visitar em Cabo Verde, desde destinos de praia até cidades vibrantes",
};

const Places = () => {
  return (
    <Layout header={2} extraClass={"pt-160"} >
        <HotelBanner pageTitle={"Lugares para Visitar"}  />
        {/*====== Start Booking Section ======*/}

        {/*====== End Booking Section ======*/}
        {/*====== Start Destination Section ======*/}
        <PlacesPage/>

        {/*====== End Destination Section ======*/}


        {/*====== Start Gallery Section ======*/}
        {/*     <GallerySection />          */}
        {/*====== End Gallery Section ======*/}
      </Layout>
  );
};
export default Places;