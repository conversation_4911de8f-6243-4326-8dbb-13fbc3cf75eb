import Link from 'next/link'
import Image from 'next/image'
import { createSlug } from '../../utils/utils'

export default function SingleService({ service, url }) {
	const baseUrl = process.env.NEXT_PUBLIC_DIRECTORY_BASE_URL
	return (
		<div className='content-container fadeInUp'>
			<div className='member-img'>
				<Image
					src={service.logo || '/assets/images/logo/yp-logo.png'}
					alt={`${service.bname} - ${service.locality} business logo`}
					title={`${service.bname} business listing in ${service.locality}`}
					width={400}
					height={400}
				/>
			</div>
			<div className='member-info'>
				<h4 className='bname'>{service.bname}</h4>
        <p className=''>{service.baddress}</p>
				<p className=''>{service.locality}</p>
				<p className=''>{service.bemail}</p>
        <br />
        <br />
				<Link
					href={`${baseUrl}/business-category/${createSlug(
						service.title
					)}/business-details/${service.nome_loc_tit_id}-${createSlug(
						service.bname
					)}`}
					target='_blank'
					className='btn btn-custom-yellow'
				>
					View Directory Listing
				</Link>
			</div>
		</div>
	)
}
