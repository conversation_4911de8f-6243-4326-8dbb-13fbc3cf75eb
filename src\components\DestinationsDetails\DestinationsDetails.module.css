/* DestinationsDetailsPage.module.css */

/* General styling for the details section */
.destinationsDetails {
    padding: 3rem 0;
    margin-top: 5rem;
  }
  
  /* Styling for the main destination image */
  .destinationImage img {
    border-radius: 8px;
    max-height: 500px;
    width: 100%;
    object-fit: cover;
  }
  
  /* Title styling */
  .destinationTitle {
    margin-top: 1rem;
    font-size: 2.5rem;
    font-weight: 600;
    color: #333;
  }
  
  /* Texts styling */
  .destinationTexts p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #555;
  }
  
  /* Overview table styling */
  .overviewTable {
    margin-top: 2rem;
  }
  
  .overviewTable td {
    font-size: 1.1rem;
    padding: 12px;
  }
  
  /* Accordion button styling */
  .accordion-button {
    background-color: #fa4836;
    color: white;
    border: none;
  }
  
  .accordion-button:not(.collapsed) {
    background-color: #fa4836;
    color: white;
  }
  
  .accordion-button::after {
    color: white;
  }
  