import React from 'react';
import AboutBanner from "../../components/AboutBanner";
import Layout from "../../layout/Layout";
import Link from "next/link";
import Image from 'next/image';
import PlacesSlider from '../../components/PlacesSlider';

export const metadata = {
  title: "Sobre Cabo Verde | Descubra Cabo Verde",
  description: "Explore as paisagens deslumbrantes de Cabo Verde, rica cultura e tradições musicais únicas. Descubra os melhores destinos de viagem e experiências em Cabo Verde.",
  openGraph: {
    title: "Sobre Cabo Verde | Descubra Cabo Verde",
    description: "Explore as paisagens deslumbrantes de Cabo Verde, rica cultura e tradições musicais únicas. Descubra os melhores destinos de viagem e experiências em Cabo Verde.",
    url: "https://www.guiadecaboverde.cv/about",
    images: ['/assets/images/about/cabo-verde.png'],
  },
};

const About = () => {
  return (
    <Layout header={2} >

      <AboutBanner pageTitle={"Sobre Cabo Verde"} />
      {/*====== Start Features Section ======*/}
      <section className="features-section pt-60 pb-0">
        <div className="container">
          <div className="row align-items-xl-center">
            <div className="col-xl-8">
              {/*=== Features Content Box ===*/}
              <div className="features-content-box pr-lg-70 mb-50 wow fadeInLeft">
                {/*=== Section Title ===*/}
                <div className="section-title mb-30">
                  <span className="sub-title">Disponibilidade</span>
                  <h3>Explore Descubra Cabo Verde</h3>
                </div>
                <p className="mb-30">
                A nossa terra é um mosaico de culturas, cada uma contribuindo para um património tão diverso quanto as paisagens que nos definem. Em cada sorriso trocado, sentirá o calor de um povo orgulhoso das suas raízes e ansioso por partilhar a magia da sua terra.
                Cabo Verde é mais do que um destino; é uma experiência à espera de se desenrolar. Quer anseie pela emoção de explorar vulcões, a tranquilidade de praias pristinas ou a adrenalina de escalar montanhas, Cabo Verde oferece uma tapeçaria de atividades para satisfazer a alma de qualquer aventureiro.
                <br/> Embarque numa viagem através do tempo enquanto explora a arquitetura colonial preservada da Cidade Velha, ou deixe que as cores vibrantes da vida selvagem marinha de Sal deixem uma marca indelével na sua memória. <br/>Para aqueles que procuram o encanto da vida citadina, Mindelo pulsa com energia, misturando modernidade com um rico contexto histórico e musical.
                Mas não são apenas os lugares que cativam corações; são as experiências únicas gravadas em cada aventura cabo-verdiana. Testemunhe a desova das tartarugas marinhas, onde centenas de tartarugas pintam as praias num espetáculo que desafia a imaginação. Envolva-se com as comunidades locais, saboreando a autenticidade das danças tradicionais e os sabores tentadores que fazem da culinária cabo-verdiana uma viagem em si mesma.

                </p>
                <Link href="#" className="main-btn filled-btn">
                  Saber Mais
                  <i className="far fa-paper-plane" />
                </Link>
              </div>
            </div>
            <div className="col-xl-4">
              <div>
                <Image
                  width={640}
                  height={960}
                    src="/assets/images/about/cabo-verde-landscape.jpg"
                    className="rounded mb-40"
                    alt="Features Image"
                  />
              </div>
            </div>
          </div>
        </div>
      </section>
      {/*====== End Features Section ======*/}

      {/*====== Start Places Section ======*/}
      <PlacesSlider />
      {/*====== End Places Section ======*/}

      {/*====== Start CTA Section ======*/}
      <section
        className="cta-bg overlay bg_cover pt-150 pb-150"
        style={{ backgroundImage: "url(assets/images/bg/cta-bg.jpg)" }}
      >
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-xl-8">
              {/*=== CTA Content Box ===*/}
              <div className="cta-content-box text-center text-white wow fadeInDown">
                <h2 className="mb-35">
                  Pronto para Viajar com Aventura Real e Desfrutar da Natureza
                </h2>
                <Link  href="/contact">
                  <div className="main-btn primary-btn">
                    Verificar Disponibilidade
                    <i className="far fa-paper-plane" />
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/*====== End CTA Section ======*/}


    </Layout>
  );
};
export default About;
