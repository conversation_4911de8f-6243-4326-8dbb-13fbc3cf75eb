import { Prompt } from 'next/font/google'
import 'bootstrap/dist/css/bootstrap.min.css'
import '../../styles/globals.css'
// Import vendor CSS files to avoid manual link warnings
import '../../public/assets/vendor/slick/slick.css'
import '../../public/assets/css/default.css'
import '../../public/assets/css/style.css'
import RootLayoutClient from './layout-client'
import Analytics from '../utils/analytics/Analytics'
import { OrganizationSchema, WebsiteSchema } from '../components/SEO/JsonLdSchema'

const prompt = Prompt({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700', '800'],
  variable: '--font-prompt',
  display: 'swap',
})

export const metadata = {
  title: {
    default: 'Seu guia definitivo de viagem | Descubra Cabo Verde',
    template: '%s | Descubra Cabo Verde'
  },
  description: 'Descubra as melhores experiências de viagem de Cabo Verde, praias paradisíacas e eventos culturais',
  metadataBase: new URL('https://www.guiadecaboverde.cv'),
  openGraph: {
    title: 'Seu guia definitivo de viagem | Descubra Cabo Verde',
    description: 'Descubra as melhores experiências de viagem de Cabo Verde, praias paradisíacas e eventos culturais',
    url: 'https://www.guiadecaboverde.cv',
    siteName: 'Descubra Cabo Verde',
    images: [
      {
        url: '/assets/images/logo/guia-turistico-logo.png',
        width: 1200,
        height: 630,
        alt: 'Guia Turístico - Descubra Cabo Verde',
      },
    ],
    locale: 'pt_CV',
    type: 'website',
  },
  icons: {
    icon: [
      { url: '/favicon.png', sizes: '32x32', type: 'image/png' },
      { url: '/assets/images/logo/guia-turistico-logo.png', sizes: '192x192', type: 'image/png' },
    ],
    shortcut: '/favicon.png',
    apple: '/assets/images/logo/guia-turistico-logo.png',
  },
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default function RootLayout({ children }) {
  return (
    <html lang="pt" className={prompt.variable}>
      <head>
        <meta charSet="utf-8" />
        <meta httpEquiv="x-ua-compatible" content="ie=edge" />

        {/* Icon Fonts - Keep as external links for better performance */}
        <link rel="stylesheet" href="/assets/fonts/fontawesome/css/all.min.css" />
        <link rel="stylesheet" href="/assets/fonts/flaticon/flaticon_gowilds.css" />
      </head>
      <body className={prompt.className}>
        <OrganizationSchema />
        <WebsiteSchema />
        <Analytics />
        <RootLayoutClient>
          {children}
        </RootLayoutClient>
      </body>
    </html>
  )
}