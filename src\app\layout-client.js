'use client'

import { Fragment, useEffect, useState } from "react"
import PreLoader from "@/src/layout/PreLoader"
import ImageView from "@/src/components/ImageView"
import VideoPopup from "@/src/components/VideoPopup"
import niceSelect from "react-nice-select"

export default function RootLayoutClient({ children }) {
  const [loader, setLoader] = useState(true)

  const handleLoader = () => {
    setTimeout(() => {
      setLoader(false)
    }, 1500)
  }

  useEffect(() => {
    handleLoader()
  }, [])

  useEffect(() => {
    niceSelect()
  }, [])

  return (
    <Fragment>
      <ImageView />
      <VideoPopup />
      {loader && <PreLoader />}
      {!loader && children}
    </Fragment>
  )
}