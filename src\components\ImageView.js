'use client'

import { Fragment, useEffect, useState } from "react";
import useClickOutside from "../useClickOutside";
import Image from "next/image";

const ImageView = () => {
  const [isOpen, setOpen] = useState(false);
  const [img, setImg] = useState("");

  let domNode = useClickOutside(() => {
    setOpen(false);
  });

  useEffect(() => {
    const a = document.querySelectorAll("a");
    a.forEach((a) => {
      if (a.href.includes("/")) {
        if (a.href.includes(".jpg")) {
          a.addEventListener("click", (e) => {
            e.preventDefault();
            setOpen(true);
            setImg(a.href);
          });
        }
      }
    });
  }, []);

  return (
    <Fragment>
      {isOpen && (
        <div className="mfp-bg mfp-ready" onClick={() => setOpen(false)}>
          <div className="mfp-wrap mfp-close-btn-in mfp-auto-cursor">
            <div className="mfp-container mfp-s-ready mfp-iframe-holder">
              <div className="mfp-content" ref={domNode}>
                <div className="mfp-iframe-scaler">
                  <Image
                    className="mfp-img"
                    src={img}
                    alt="Visualização da imagem em destaque"
                    title="Imagem ampliada"
                    fill
                    style={{ objectFit: 'contain' }}
                  />
                  <button
                    title="Close (Esc)"
                    type="button"
                    className="mfp-close"
                    onClick={() => setOpen(false)}
                  >
                    ×
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </Fragment>
  );
};

export default ImageView;
