import Head from 'next/head';
import { NextSeo } from 'next-seo';

const EnhancedSEO = ({ 
  title, 
  description, 
  url, 
  image, 
  type = 'website',
  article = null,
  breadcrumbs = null,
  noindex = false,
  canonical = null
}) => {
  const baseUrl = 'https://www.guiadecaboverde.cv';
  const fullUrl = url?.startsWith('http') ? url : `${baseUrl}${url || ''}`;
  const fullImage = image?.startsWith('http') ? image : `${baseUrl}${image || '/assets/images/logo/guia-turistico-logo.png'}`;
  const canonicalUrl = canonical || fullUrl;

  return (
    <>
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="robots" content={noindex ? "noindex, nofollow" : "index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1"} />
        <link rel="canonical" href={canonicalUrl} />
        
        {/* Enhanced Meta Tags */}
        <meta name="author" content="Descubra Cabo Verde" />
        <meta name="language" content="pt-CV" />
        <meta name="geo.region" content="CV" />
        <meta name="geo.country" content="Cabo Verde" />
        <meta name="distribution" content="global" />
        <meta name="rating" content="general" />
        
        {/* Twitter Card Meta Tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:site" content="@DescubraCaboVerde" />
        <meta name="twitter:creator" content="@DescubraCaboVerde" />
        <meta name="twitter:title" content={title} />
        <meta name="twitter:description" content={description} />
        <meta name="twitter:image" content={fullImage} />
        <meta name="twitter:image:alt" content={title} />
        <meta name="twitter:url" content={fullUrl} />
        
        {/* Additional Open Graph Tags */}
        <meta property="og:site_name" content="Descubra Cabo Verde" />
        <meta property="og:locale" content="pt_CV" />
        <meta property="og:locale:alternate" content="en_US" />
        
        {/* Article specific meta tags */}
        {article && (
          <>
            <meta property="article:author" content={article.author || "Descubra Cabo Verde"} />
            <meta property="article:published_time" content={article.publishedTime} />
            <meta property="article:modified_time" content={article.modifiedTime} />
            <meta property="article:section" content="Travel" />
            {article.tags && article.tags.map((tag, index) => (
              <meta key={index} property="article:tag" content={tag} />
            ))}
          </>
        )}
        
        {/* Breadcrumb JSON-LD */}
        {breadcrumbs && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify({
                "@context": "https://schema.org",
                "@type": "BreadcrumbList",
                "itemListElement": breadcrumbs.map((breadcrumb, index) => ({
                  "@type": "ListItem",
                  "position": index + 1,
                  "name": breadcrumb.name,
                  "item": breadcrumb.url?.startsWith('http') ? breadcrumb.url : `${baseUrl}${breadcrumb.url}`
                }))
              })
            }}
          />
        )}
      </Head>
      
      <NextSeo
        title={title}
        description={description}
        canonical={canonicalUrl}
        noindex={noindex}
        openGraph={{
          url: fullUrl,
          title: title,
          description: description,
          images: [
            {
              url: fullImage,
              width: 1200,
              height: 630,
              alt: title,
            },
          ],
          site_name: 'Descubra Cabo Verde',
          type: type,
          locale: 'pt_CV',
          ...(article && {
            article: {
              publishedTime: article.publishedTime,
              modifiedTime: article.modifiedTime,
              authors: [article.author || "Descubra Cabo Verde"],
              section: 'Travel',
              tags: article.tags || []
            }
          })
        }}
        twitter={{
          handle: '@DescubraCaboVerde',
          site: '@DescubraCaboVerde',
          cardType: 'summary_large_image',
        }}
        additionalMetaTags={[
          {
            name: 'keywords',
            content: article?.tags?.join(', ') || 'Cabo Verde, turismo, viagem, praias, cultura, hotéis, restaurantes, destinos'
          },
          {
            name: 'theme-color',
            content: '#FA4836'
          }
        ]}
      />
    </>
  );
};

export default EnhancedSEO;
