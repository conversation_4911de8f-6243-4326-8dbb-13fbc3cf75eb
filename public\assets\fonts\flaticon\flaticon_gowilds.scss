$flaticon_tdk-font: "flaticon_tdk";

@font-face {
    font-family: $flaticon_tdk-font;
    src: url("./flaticon_tdk.ttf?d0235de0bbc7ec3e6bca4c8fddee355a") format("truetype"),
url("./flaticon_tdk.woff?d0235de0bbc7ec3e6bca4c8fddee355a") format("woff"),
url("./flaticon_tdk.woff2?d0235de0bbc7ec3e6bca4c8fddee355a") format("woff2"),
url("./flaticon_tdk.eot?d0235de0bbc7ec3e6bca4c8fddee355a#iefix") format("embedded-opentype"),
url("./flaticon_tdk.svg?d0235de0bbc7ec3e6bca4c8fddee355a#flaticon_tdk") format("svg");
}

i[class^="flaticon-"]:before, i[class*=" flaticon-"]:before {
    font-family: flaticon_tdk !important;
    font-style: normal;
    font-weight: normal !important;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

$flaticon_tdk-map: (
    "helmet": "\f101",
    "best-price": "\f102",
    "travel": "\f103",
    "right-quote": "\f104",
    "flight": "\f105",
    "camp": "\f106",
    "blanket": "\f107",
    "cat": "\f108",
    "tent": "\f109",
    "fire": "\f10a",
    "rabbit": "\f10b",
    "wifi-router": "\f10c",
    "solar-energy": "\f10d",
    "cycling": "\f10e",
    "fishing": "\f10f",
    "gym": "\f110",
    "hiking": "\f111",
    "tent-1": "\f112",
    "reviews": "\f113",
    "award": "\f114",
    "quote": "\f115",
    "hotel": "\f116",
    "cable-car": "\f117",
    "trailer": "\f118",
    "firewood": "\f119",
    "biking-mountain": "\f11a",
    "fishing-1": "\f11b",
    "fishing-2": "\f11c",
    "caravan": "\f11d",
    "world": "\f11e",
    "journey": "\f11f",
    "gps": "\f120",
    "paper-plane": "\f121",
);

.flaticon-helmet:before {
    content: map-get($flaticon_tdk-map, "helmet");
}
.flaticon-best-price:before {
    content: map-get($flaticon_tdk-map, "best-price");
}
.flaticon-travel:before {
    content: map-get($flaticon_tdk-map, "travel");
}
.flaticon-right-quote:before {
    content: map-get($flaticon_tdk-map, "right-quote");
}
.flaticon-flight:before {
    content: map-get($flaticon_tdk-map, "flight");
}
.flaticon-camp:before {
    content: map-get($flaticon_tdk-map, "camp");
}
.flaticon-blanket:before {
    content: map-get($flaticon_tdk-map, "blanket");
}
.flaticon-cat:before {
    content: map-get($flaticon_tdk-map, "cat");
}
.flaticon-tent:before {
    content: map-get($flaticon_tdk-map, "tent");
}
.flaticon-fire:before {
    content: map-get($flaticon_tdk-map, "fire");
}
.flaticon-rabbit:before {
    content: map-get($flaticon_tdk-map, "rabbit");
}
.flaticon-wifi-router:before {
    content: map-get($flaticon_tdk-map, "wifi-router");
}
.flaticon-solar-energy:before {
    content: map-get($flaticon_tdk-map, "solar-energy");
}
.flaticon-cycling:before {
    content: map-get($flaticon_tdk-map, "cycling");
}
.flaticon-fishing:before {
    content: map-get($flaticon_tdk-map, "fishing");
}
.flaticon-gym:before {
    content: map-get($flaticon_tdk-map, "gym");
}
.flaticon-hiking:before {
    content: map-get($flaticon_tdk-map, "hiking");
}
.flaticon-tent-1:before {
    content: map-get($flaticon_tdk-map, "tent-1");
}
.flaticon-reviews:before {
    content: map-get($flaticon_tdk-map, "reviews");
}
.flaticon-award:before {
    content: map-get($flaticon_tdk-map, "award");
}
.flaticon-quote:before {
    content: map-get($flaticon_tdk-map, "quote");
}
.flaticon-camping:before {
    content: map-get($flaticon_tdk-map, "hotel");
}
.flaticon-cable-car:before {
    content: map-get($flaticon_tdk-map, "cable-car");
}
.flaticon-trailer:before {
    content: map-get($flaticon_tdk-map, "trailer");
}
.flaticon-firewood:before {
    content: map-get($flaticon_tdk-map, "firewood");
}
.flaticon-biking-mountain:before {
    content: map-get($flaticon_tdk-map, "biking-mountain");
}
.flaticon-fishing-1:before {
    content: map-get($flaticon_tdk-map, "fishing-1");
}
.flaticon-fishing-2:before {
    content: map-get($flaticon_tdk-map, "fishing-2");
}
.flaticon-caravan:before {
    content: map-get($flaticon_tdk-map, "caravan");
}
.flaticon-world:before {
    content: map-get($flaticon_tdk-map, "world");
}
.flaticon-journey:before {
    content: map-get($flaticon_tdk-map, "journey");
}
.flaticon-gps:before {
    content: map-get($flaticon_tdk-map, "gps");
}
.flaticon-paper-plane:before {
    content: map-get($flaticon_tdk-map, "paper-plane");
}
