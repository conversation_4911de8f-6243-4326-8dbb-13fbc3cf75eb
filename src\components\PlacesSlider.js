'use client'

import React from 'react';
import Slider from "react-slick";
import Link from "next/link";
import Image from 'next/image';
import { sliderActive3ItemDot } from "../sliderProps";
import places from "../data/places";

const PlacesSlider = () => {
  return (
    <section className="places-seciton pt-35 pb-50 mb50">
      <div className="container">
        <div className="row">
          <div className="col-lg-12">
            {/*====== Section Title ======*/}
            <div className="section-title text-center mb-45 wow fadeInDown">
              <h2>Tours em Destaque</h2>
            </div>
          </div>
        </div>
        <Slider
          {...sliderActive3ItemDot}
          className="slider-active-3-item-dot wow fadeInUp "
        >
          {places.map((place) => (
            <div key={place.id} className="single-place-item mb-60">
              <div className="place-img">
                <Image width={410} height={280} src={place.image} alt={place.name} />
              </div>
              <div className="place-content">
                <div className="info">
                  <ul className="ratings">
                    {Array(5)
                      .fill()
                      .map((_, index) => (
                        <li key={index}>
                          <i className="fas fa-star" />
                        </li>
                      ))}
                    <li>
                      <Link href="#">({place.rating})</Link>
                    </li>
                  </ul>
                  <h4 className="title">
                    {place.name}
                  </h4>
                  <p className="location">
                    <i className="fas fa-map-marker-alt" />
                    {place.location}
                  </p>

                  <div className="meta">
                    <span>
                      <i className="far fa-clock" />
                      {place.duration}
                    </span>
                    <span>
                      <i className="far fa-user" />
                      {place.capacity}
                    </span>
                    <span>
                      {/*  <Link  href="/tour-details">
                          <Link>
                            Detalhes
                            <i className="far fa-long-arrow-right" />
                          </Link>
                        </Link> */}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </Slider>
      </div>
    </section>
  );
};

export default PlacesSlider;