import RestaurantsPage from "../../components/RestaurantsPage/RestaurantsPage";
import RestaurantBanner from "../../components/RestaurantBanner";
import Layout from "../../layout/Layout";
import Link from "next/link";
import Description from "../../components/Description/Description";

export const metadata = {
  title: "Restaurantes | Descubra Cabo Verde",
  description: "Descubra os melhores restaurantes de Cabo Verde, desde restaurantes com vista para o oceano até diferentes cozinhas",
};

const Restaurants = () => {
  const title = "Restaurantes em Cabo Verde";
  const description =
    "Cabo Verde oferece uma cena culinária vibrante com uma mistura de sabores locais e internacionais. Desde estabelecimentos de alta gastronomia em Praia até restaurantes à beira-mar servindo peixe fresco em Mindelo, há algo para todos os paladares. Desfrute de pratos tradicionais cabo-verdianos como Cachupa e peixe grelhado, ou explore cozinhas internacionais em ambientes elegantes. Quer procure um café casual ou uma experiência gastronómica luxuosa, os restaurantes de Cabo Verde oferecem sabores inesquecíveis e hospitalidade autêntica.";
  const features = [
    "Culinária Autêntica Cabo-Verdiana",
    "Experiências de Alta Gastronomia",
    "Opções de Peixe e Marisco Fresco",
    "Menus Vegetarianos e Veganos",
    "Restaurantes Familiares",
    "Restaurantes com Vista para o Oceano",
    "Petiscos e Comida de Rua",
    "Cozinhas Internacionais (Portuguesa, Francesa, Italiana, etc.)",
    "Jantares Românticos à Luz de Velas",
    "Serviços de Take-away e Entrega",
    "Música Tradicional ao Vivo",
    "Terraços com Vista Panorâmica"
  ];

  return (
    <Layout header={2} extraClass={"pt-160"}>
        <RestaurantBanner pageTitle={"Restaurantes"} />
        {/*====== Start Destination Details Section ======*/}
        <Description title={title} description={description} features={features} />
        {/*====== End Destination Details Section ======*/}

        <RestaurantsPage />
        {/*====== Start CTA Section ======*/}
        <section
          className="cta-bg overlay bg_cover pt-150 pb-150 "
          style={{ backgroundImage: "url(assets/images/bg/cta-bg2.jpg)" }}
        >
          <div className="container ">
            <div className="row align-items-center">
              <div className="col-xl-7 col-lg-8">
                {/*=== CTA Content Box ===*/}
                <div className="cta-content-box text-white wow fadeInLeft">
                  <h2 className="mb-35">
                    Pronto para Viajar com Aventura Real e Desfrutar da Natureza
                  </h2>
                  <Link href="/about">
                    <div className="main-btn primary-btn">
                      Verificar Disponibilidade
                      <i className="far fa-paper-plane" />
                    </div>
                  </Link>
                </div>
              </div>
              <div className="col-xl-5 col-lg-4">
                {/*=== Play Box ===*/}
                <div className="play-box text-lg-end text-center wow fadeInRight">
                  <div
                    href="https://www.youtube.com/watch?v=ibuUmMhD2Pg"
                    className="video-popup"
                  >
                    <i className="fas fa-play" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/*====== End CTA Section ======*/}

        {/*====== Start Gallery Section ======*/}
                 {/*      <GallerySection className="mt-100" />     */}
      </Layout>
  );
};
export default Restaurants;