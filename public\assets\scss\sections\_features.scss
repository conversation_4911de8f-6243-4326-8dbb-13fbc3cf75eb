
/*---==================
    05. Features css 
=================----*/

.ratings{
    & li{
        display: inline-block;
        & i{
            @extend %secondary-color;
        }
    }
}

.ribbon{
    font: 500 18px $font;
    @extend %secondary-bg;
    padding: 10px 20px;
    @extend %white-color;
}

.radius-60{
    @include border-radius(60px);
}
.radius-12{
    @include border-radius(12px);
}
.radius-top-left-right-288{
    @include border-radius(288px 288px 0px 0px);
}

.video-popup{
    position: relative;
    width: 110px;
    height: 110px;
    font-size: 14px;
    @extend %flex-center;
    @extend %white-bg;
    display: inline-flex;
    @include border-radius(50%);
    &:after,
    &:before{
        position: absolute;
        left: 0;
        top: 0;
        content: "";
        @include border-radius(50%);
        width: 100%;
        height: 100%;
        border: 1px solid #e1e1e1;
        animation: playpopup infinite linear 1s;
    }
    &:before{
        animation: playpopup infinite linear 2s;
    }
}
@-webkit-keyframes playpopup {
    0% {
        transform: scale(1);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.3);
        opacity: 0.3;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}
@keyframes playpopup {
    0% {
        transform: scale(1);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.5);
        opacity: 0.3;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

.check-list{
    & li{
        display: flex;
        align-items: center;
        font: 400 18px $font;
        @extend %heading;
        &:not(:last-child){
            margin-bottom: 20px;
        }
        & i{
            @extend %primary-color;
            margin-right: 15px;
            font-size: 20px;
        }
    }
}

.features-one_image-box{
    margin-left: -90px;
    @media #{$xm}{
        margin-left: 0;
        text-align: center;
    }
    & img{
        border-radius: 305px 305px 0px 0px;
    }
}

.features-item-area{
    & > .row{
        & .col-md-6{
            &:nth-child(2){
                margin-top: -30px;
                @media #{$xs}{
                    margin-top: 0;
                }
            }
            &:nth-child(4){
                margin-top: -30px;
                @media #{$xs}{
                    margin-top: 0;
                }
            }
        }
    }
}


.features-list_one{
    & .single-features-list{
        &:last-child{
            & .icon-inner{
                & .icon{
                    &:after{
                        display: none;
                    }
                }
            }
        }
    }
}