'use client'

import React, { useState, useEffect } from "react";
import axios from "axios";
import Pagination from "../Pagination/Pagination";
import { Container, Row, Col } from "react-bootstrap";
import Description from "../Description/Description";
import PreLoader from "../../layout/PreLoader";
import SingleService from "../SingleService/SingleService";

const DestinationsPage = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const hotelsPerPage = 10;
  const [hotels, setHotels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Calculate indices for current hotels
  const indexOfLastHotel = currentPage * hotelsPerPage;
  const indexOfFirstHotel = indexOfLastHotel - hotelsPerPage;
  const currentHotels = hotels.slice(indexOfFirstHotel, indexOfLastHotel);

  const onPageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  useEffect(() => {
    const fetchHotels = async () => {
      try {
        setLoading(true);
        const response = await axios.post(
          "https://api.yellowpageskenya.com/v1/search",
          {
            searchParameters: {
              LINGUA_ID: 2,
              PESQUISA_F: "hotels",
              LOCALITY_F: "",
              TITULO_ID: "",
              country: "CBV",
            },
          },
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        setHotels(response.data.results || []);
      } catch (error) {
        setError(error.message || "Ocorreu um erro ao buscar os dados");
      } finally {
        setLoading(false);
      }
    };

    fetchHotels();
  }, []);

  const title = "Hotéis em Cabo Verde";
  const description =
    "Cabo Verde oferece uma gama diversa de hotéis, desde resorts de luxo com vista para o oceano Atlântico até acolhedores alojamentos nas vibrantes cidades de Praia e Mindelo. Os visitantes podem escolher entre resorts de praia na ilha do Sal, hotéis boutique em Santiago, ou pousadas familiares, garantindo uma estadia confortável adaptada às suas preferências e às paisagens únicas de Cabo Verde.";
  const features = [
    "Alojamento Familiar",
    "Turismo Rural",
    "Pesca Desportiva",
    "Caminhadas e Trilhas",
    "Wi-fi Gratuito",
    "Resorts de Luxo à Beira-Mar",
    "Hotéis com Vista para o Oceano Atlântico",
    "Hotéis Boutique com Serviço Personalizado",
    "Pousadas Económicas",
    "Suítes Familiares e Comodidades para Crianças",
    "Instalações para Conferências e Eventos",
    "Pacotes Tudo Incluído",
    "Piscinas Infinitas com Vistas Panorâmicas",
    "Centros de Bem-estar e Spas",
    "Experiências de Música Tradicional",
    "Tours de Vulcões e Natureza"
  ];

  return (
    <div>
        <section className="content-container pt-100">
      <Container>
        <Description
          title={title}
          description={description}
          features={features}
        />
        {loading ? (
         <PreLoader/>
        ) : error ? (
          <p>Error: {error}</p>
        ) : (
          <>
            <Row className="restaurant-parent-container">
              {currentHotels.map((destination) => (
                   <SingleService service={destination} key={destination.nome_loc_tit_id} url={"https://www.yellowpageskenya.com/business-category/hotels/business-details"}/>
               ))}
            </Row>

            <div className="wow fadeInDown mt-60 mb-30 d-flex justify-content-center">
              <Pagination
                currentPage={currentPage}
                totalPages={Math.ceil(hotels.length / hotelsPerPage)}
                onPageChange={onPageChange}
              />
            </div>
          </>
        )}
      </Container>
    </section>
    </div>
  );
};

export default DestinationsPage;
