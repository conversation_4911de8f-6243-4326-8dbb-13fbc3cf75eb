import Layout from "../../layout/Layout";
import HotelBanner from "../../components/HotelBanner";
import DestinationsPage from "../../components/DestinationsPage/destinations";

export const metadata = {
  title: "Destinos | Descubra Cabo Verde",
  description: "Descubra os Destinos de Cabo Verde desde Praias Paradisíacas até Aventuras e Viagens Inesquecíveis",
  openGraph: {
    title: "Destinos em Cabo Verde | Descubra Cabo Verde",
    description: "Descubra os Destinos de Cabo Verde desde Praias Paradisíacas até Aventuras e Viagens Inesquecíveis",
    url: "https://www.guiadecaboverde.cv/destinations",
    images: ['/assets/images/features/destinations.png'],
  },
};

const Destinations = () => {
  return (
    <Layout header={2} extraClass={"pt-10"} >
        <HotelBanner pageTitle={"Destinos de Cabo Verde"}  />

        <section>
          <DestinationsPage />
        </section>

      </Layout>
  );
};

export default Destinations;