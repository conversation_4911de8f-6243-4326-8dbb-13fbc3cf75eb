import Head from 'next/head';

// Organization Schema for the main website
export const OrganizationSchema = () => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "TravelAgency",
    "name": "Descubra Cabo Verde",
    "alternateName": "Guia Turístico Cabo Verde",
    "url": "https://www.guiadecaboverde.cv",
    "logo": "https://www.guiadecaboverde.cv/assets/images/logo/guia-turistico-logo.png",
    "description": "Seu guia definitivo de viagem para Cabo Verde. Descubra as melhores experiências de viagem, praias paradisíacas e eventos culturais.",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "CV",
      "addressRegion": "Cabo Verde"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": ["Portuguese", "English"]
    },
    "sameAs": [
      "https://www.facebook.com/DescubraCaboVerde",
      "https://www.instagram.com/DescubraCaboVerde"
    ],
    "areaServed": {
      "@type": "Country",
      "name": "Cabo Verde"
    },
    "serviceType": [
      "Travel Guide",
      "Tourism Information",
      "Hotel Booking",
      "Restaurant Recommendations",
      "Tourist Attractions"
    ]
  };

  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
      />
    </Head>
  );
};

// Website Schema for the main site
export const WebsiteSchema = () => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Descubra Cabo Verde",
    "url": "https://www.guiadecaboverde.cv",
    "description": "Seu guia definitivo de viagem para Cabo Verde",
    "publisher": {
      "@type": "Organization",
      "name": "Descubra Cabo Verde",
      "logo": {
        "@type": "ImageObject",
        "url": "https://www.guiadecaboverde.cv/assets/images/logo/guia-turistico-logo.png"
      }
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://www.guiadecaboverde.cv/search?q={search_term_string}"
      },
      "query-input": "required name=search_term_string"
    }
  };

  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
      />
    </Head>
  );
};

// Tourist Destination Schema
export const TouristDestinationSchema = ({ destination }) => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "TouristDestination",
    "name": destination.title,
    "description": destination.description,
    "image": destination.image ? `https://www.guiadecaboverde.cv${destination.image}` : null,
    "url": `https://www.guiadecaboverde.cv/destinations-details/${destination.id}`,
    "geo": destination.coordinates ? {
      "@type": "GeoCoordinates",
      "latitude": destination.coordinates.lat,
      "longitude": destination.coordinates.lng
    } : null,
    "containedInPlace": {
      "@type": "Country",
      "name": "Cabo Verde"
    },
    "touristType": destination.category || "General Tourism"
  };

  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
      />
    </Head>
  );
};

// Hotel Schema
export const HotelSchema = ({ hotel }) => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "Hotel",
    "name": hotel.bname || hotel.title,
    "description": hotel.description || `Hotel em ${hotel.locality || hotel.location}`,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": hotel.baddress || hotel.address,
      "addressLocality": hotel.locality || hotel.location,
      "addressCountry": "CV"
    },
    "telephone": hotel.phone,
    "email": hotel.bemail || hotel.email,
    "url": hotel.website,
    "image": hotel.logo || hotel.image,
    "priceRange": hotel.priceRange || "$$",
    "starRating": hotel.rating ? {
      "@type": "Rating",
      "ratingValue": hotel.rating
    } : null
  };

  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
      />
    </Head>
  );
};

// Restaurant Schema
export const RestaurantSchema = ({ restaurant }) => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "Restaurant",
    "name": restaurant.bname || restaurant.title,
    "description": restaurant.description || `Restaurante em ${restaurant.locality || restaurant.location}`,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": restaurant.baddress || restaurant.address,
      "addressLocality": restaurant.locality || restaurant.location,
      "addressCountry": "CV"
    },
    "telephone": restaurant.phone,
    "email": restaurant.bemail || restaurant.email,
    "url": restaurant.website,
    "image": restaurant.logo || restaurant.image,
    "priceRange": restaurant.priceRange || "$$",
    "servesCuisine": restaurant.cuisine || "Cabo Verdean",
    "aggregateRating": restaurant.rating ? {
      "@type": "AggregateRating",
      "ratingValue": restaurant.rating,
      "reviewCount": restaurant.reviewCount || 1
    } : null
  };

  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
      />
    </Head>
  );
};

// Article Schema for Blog Posts
export const ArticleSchema = ({ article, url }) => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": article.title,
    "description": article.content?.introduction || article.description,
    "image": article.banner?.image ? `https://www.guiadecaboverde.cv${article.banner.image}` : null,
    "author": {
      "@type": "Organization",
      "name": article.meta?.author || "Descubra Cabo Verde"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Descubra Cabo Verde",
      "logo": {
        "@type": "ImageObject",
        "url": "https://www.guiadecaboverde.cv/assets/images/logo/guia-turistico-logo.png"
      }
    },
    "datePublished": article.meta?.date ? new Date(article.meta.date).toISOString() : new Date().toISOString(),
    "dateModified": article.meta?.dateModified ? new Date(article.meta.dateModified).toISOString() : new Date().toISOString(),
    "url": url,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": url
    },
    "articleSection": "Travel",
    "keywords": article.sidebar?.tags?.join(", ") || "Cabo Verde, Viagem, Turismo"
  };

  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
      />
    </Head>
  );
};

// Breadcrumb Schema
export const BreadcrumbSchema = ({ breadcrumbs }) => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((breadcrumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": breadcrumb.name,
      "item": breadcrumb.url
    }))
  };

  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
      />
    </Head>
  );
};

// Things to Do / Activity Schema
export const ActivitySchema = ({ activity }) => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "TouristAttraction",
    "name": activity.title,
    "description": activity.description,
    "image": activity.image ? `https://www.guiadecaboverde.cv${activity.image}` : null,
    "url": `https://www.guiadecaboverde.cv/things-to-do-details/${activity.id}`,
    "address": {
      "@type": "PostalAddress",
      "addressLocality": activity.location,
      "addressCountry": "CV"
    },
    "openingHours": activity.operatingTime,
    "touristType": activity.category || "General Tourism",
    "containedInPlace": {
      "@type": "Country",
      "name": "Cabo Verde"
    }
  };

  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
      />
    </Head>
  );
};

// FAQ Schema
export const FAQSchema = ({ faqs }) => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };

  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
      />
    </Head>
  );
};
