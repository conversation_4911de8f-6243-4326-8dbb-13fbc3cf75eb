// Sticky nav
export const stickyNav = () => {
  window.addEventListener("scroll", () => {
    let offset = window.scrollY;
    const sticky = document.querySelectorAll(".header-navigation");
    for (let i = 0; i < sticky.length; i++) {
      const stick = sticky[i];
      if (stick) {
        if (offset > 10) {
          stick.classList.add("sticky");
        } else {
          stick.classList.remove("sticky");
        }
      }
    }
  });
};

// animation
// export const animation = () => {
//   if (typeof window !== "undefined") {
//     window.WOW = require("wowjs");
//   }
//   new WOW().init();
// };

export const createSlug = (inputString) => {
	if (!inputString || typeof inputString !== 'string') {
		return ''
	}

	// Convert to lowercase and replace spaces with hyphens
	let slug = inputString.toLowerCase().replace(/\s+/g, '-')

	// Remove special characters and non-alphanumeric characters
	slug = slug.replace(/[^\w-]+/g, '')

	// Remove consecutive dashes
	slug = slug.replace(/-{2,}/g, '-')

	return slug
}