'use client'

import Link from "next/link";
import { Fragment, useState } from "react";
import SearchModal from "./SearchModal";

const Menu = () => {
  return (
    <Fragment>
      <DeskTopMenu />
      <MobileMenu />
    </Fragment>
  );
};
export default Menu;

const DeskTopMenu = () => {
  //const [searchModal, setSearchModal] = useState(false);
  return (
    <Fragment>
      <nav className="main-menu d-none d-xl-block">
        <ul>
          <li className="menu-item has-children">
            <Link href="/">
              Início

            </Link>
          </li>
          <li className="menu-item has-children">
            <Link href="/about">
              Sobre Cabo Verde

            </Link>
          </li>

          <li className="menu-item has-children">
            <Link href="#">
              Atividades
              <span className="dd-trigger">
                <i className="far fa-angle-down" />
              </span>
            </Link>

            <ul className="sub-menu">
              <li>
                <Link href="/hotels">Hotéis</Link>
              </li>
              <li>
                <Link href="/restaurants">Restaurantes</Link>
              </li>
              <li>
                <Link href="/places">Lugares</Link>
              </li>
            </ul>
          </li>
          <li className="menu-item has-children">
            <Link href="/blog">
              Blog
            </Link>
          </li>
          <li className="menu-item has-children">
            <Link href="/faqs">
              Perguntas Frequentes

            </Link>
          </li>
          <li className="menu-item has-children">
            <Link href="/contact">
              Contactar

            </Link>
          </li>
        </ul>
      </nav>
    </Fragment>
  );
};

const MobileMenu = () => {
  const [activeMenu, setActiveMenu] = useState("");
  const activeMenuSet = (value) =>
      setActiveMenu(activeMenu === value ? "" : value),
    activeLi = (value) =>
      value === activeMenu ? { display: "block" } : { display: "none" };
  return (
    <nav className="main-menu d-block d-xl-none">
      <ul>
        <li className="menu-item has-children">
          <Link href="/">Início</Link>
        </li>
        <li className="menu-item has-children">
          <Link href="about">Sobre Cabo Verde</Link>
        </li>
        <li className="menu-item has-children">
          <Link href="#">
            Atividades
            <span
              className="dd-trigger"
              onClick={() => activeMenuSet("Destination")}
            >
              <i className="far fa-angle-down" />
            </span>
          </Link>
          <ul className="sub-menu" style={activeLi("Destination")}>
            <li>
              <Link href="hotels">Hotéis</Link>
            </li>
            <li>
              <Link href="restaurants">Restaurantes</Link>
            </li>
            <li>
              <Link href="places">Lugares para Visitar</Link>
            </li>
          </ul>
        </li>
        <li className="menu-item has-children">
          <Link href="blog">Blog</Link>
        </li>

        <li className="menu-item has-children">
          <Link href="contact">Contactar</Link>
        </li>

      </ul>
    </nav>
  );
};
