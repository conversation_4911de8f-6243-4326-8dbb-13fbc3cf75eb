'use client';
import Slider from "react-slick";
import { sliderActive5Item } from "../sliderProps";
import Image from "next/image";
const GallerySection = () => {
  return (
    <section className="gallery-section mbm-150">
      <div className="container-fluid">
        <Slider
          {...sliderActive5Item}
          className="slider-active-5-item wow fadeInUp"
        >
          {/*=== Single Gallery Item ===*/}
          <div className="single-gallery-item">
            <div className="gallery-img">
              <Image fill={true} src="/assets/images/blog/diani.jpeg" alt="Praia paradisíaca em Cabo Verde - Paisagem tropical com águas cristalinas" title="Praias de Cabo Verde - Destino turístico tropical" />
              <div className="hover-overlay">
                <div
                  href="/assets/images/gallery/gl-1.jpg"
                  className="icon-btn img-popup"
                >
                  <i className="far fa-plus" />
                </div>
              </div>
            </div>
          </div>
          {/*=== Single Gallery Item ===*/}
          <div className="single-gallery-item">
            <div className="gallery-img">
              <Image fill={true} src="/assets/images/gallery/gl-2.jpg" alt="Paisagem vulcânica de Cabo Verde - Montanhas e natureza exuberante" title="Paisagens naturais de Cabo Verde" />
              <div className="hover-overlay">
                <div
                  href="/assets/images/gallery/gl-2.jpg"
                  className="icon-btn img-popup"
                >
                  <i className="far fa-plus" />
                </div>
              </div>
            </div>
          </div>
          {/*=== Single Gallery Item ===*/}
          <div className="single-gallery-item">
            <div className="gallery-img">
              <Image fill={true} src="/assets/images/gallery/gl-3.jpg" alt="Cultura e tradições de Cabo Verde - Vida local e património" title="Cultura cabo-verdiana autêntica" />
              <div className="hover-overlay">
                <div
                  href="/assets/images/gallery/gl-3.jpg"
                  className="icon-btn img-popup"
                >
                  <i className="far fa-plus" />
                </div>
              </div>
            </div>
          </div>
          {/*=== Single Gallery Item ===*/}
          <div className="single-gallery-item">
            <div className="gallery-img">
              <Image fill={true} src="/assets/images/gallery/gl-4.jpg" alt="Arquitetura colonial de Cabo Verde - Edifícios históricos e património" title="Património arquitetónico de Cabo Verde" />
              <div className="hover-overlay">
                <div
                  href="/assets/images/gallery/gl-4.jpg"
                  className="icon-btn img-popup"
                >
                  <i className="far fa-plus" />
                </div>
              </div>
            </div>
          </div>
          {/*=== Single Gallery Item ===*/}
          <div className="single-gallery-item">
            <div className="gallery-img">
              <Image fill={true} src="/assets/images/gallery/gl-5.jpg" alt="Atividades aquáticas em Cabo Verde - Desportos e aventuras no oceano" title="Desportos aquáticos em Cabo Verde" />
              <div className="hover-overlay">
                <div
                  href="/assets/images/gallery/gl-5.jpg"
                  className="icon-btn img-popup"
                >
                  <i className="far fa-plus" />
                </div>
              </div>
            </div>
          </div>
          {/*=== Single Gallery Item ===*/}
          <div className="single-gallery-item">
            <div className="gallery-img">
              <Image fill={true} src="/assets/images/gallery/gl-3.jpg" alt="Vida noturna e entretenimento em Cabo Verde - Música e festivais locais" title="Entretenimento e música cabo-verdiana" />
              <div className="hover-overlay">
                <div
                  href="/assets/images/gallery/gl-3.jpg"
                  className="icon-btn img-popup"
                >
                  <i className="far fa-plus" />
                </div>
              </div>
            </div>
          </div>
        </Slider>
      </div>
    </section>
  );
};
export default GallerySection;
