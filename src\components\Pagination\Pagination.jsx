import Link from "next/link";
import React from "react";

const Pagination = ({ currentPage, totalPages, onPageChange }) => {
  const handlePageChange = (newPage, e) => {
    e.preventDefault();
    if (newPage >= 1 && newPage <= totalPages) {
      onPageChange(newPage);
    }
  };

  const renderPageNumbers = () => {
    const pageNumbers = [];
    const pagesToShow = 5;

    // Always show the first page
    pageNumbers.push(
      <li key={1} className={`me-2 ${1 === currentPage ? "active" : ""}`}>
        <Link className="pagination__link"
          href="#"
          onClick={(e) => handlePageChange(1, e)}
          aria-label="Go to page 1"
          aria-current={1 === currentPage ? "page" : undefined}
        >
          1
        </Link>
      </li>
    );

    // Add ellipsis if there are more than pagesToShow + 1 pages and we're not at the start
    if (totalPages > pagesToShow + 1 && currentPage > 3) {
      pageNumbers.push(
        <li key="ellipsis-start" className="me-2">
          <span aria-hidden="true">...</span>
        </li>
      );
    }

    // Calculate the range of pages to show
    let startPage = Math.max(2, currentPage - Math.floor(pagesToShow / 2));
    let endPage = Math.min(startPage + pagesToShow - 1, totalPages - 1);

    // Adjust if we're near the end
    if (endPage - startPage < pagesToShow - 1) {
      startPage = Math.max(2, endPage - pagesToShow + 1);
    }

    // Render the middle pages
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(
        <li key={i} className={`me-2 ${i === currentPage ? "active" : ""}`}>
          <Link className="pagination__link"
            href="#"
            onClick={(e) => handlePageChange(i, e)}
            aria-label={`Go to page ${i}`}
            aria-current={i === currentPage ? "page" : undefined}
          >
            {i}
          </Link>
        </li>
      );
    }

    // Add ellipsis if there are more pages after the ones we've shown
    if (endPage < totalPages - 1) {
      pageNumbers.push(
        <li key="ellipsis-end" className="me-2">
          <span aria-hidden="true">...</span>
        </li>
      );
    }

    // Always show the last page if it's not already included
    if (totalPages > 1 && endPage <totalPages) {
      pageNumbers.push(
        <li key={totalPages} className={`me-2 ${totalPages === currentPage ? "active" : ""}`}>
          <Link className="pagination__link"
            href="#"
            onClick={(e) => handlePageChange(totalPages, e)}
            aria-label={`Go to page ${totalPages}`}
            aria-current={totalPages === currentPage ? "page" : undefined}
          >
            {totalPages}
          </Link>
        </li>
      );
    }

    return <div> {pageNumbers} </div> 
  };

  return (
    <nav aria-label="Pagination" className="d-flex justify-content-center">
      <ul className="tdk-pagination wow fadeInDown mt-60 mb-30">
        <li className="me-2">
          <Link
            data-pagination-link
            className="pagination__link"
            href="#"
            onClick={(e) => handlePageChange(currentPage - 1, e)}
            style={{ pointerEvents: currentPage === 1 ? "none" : "auto" }}
            aria-label="Go to previous page"
          >
            <i className="far fa-arrow-left" aria-hidden="true" />
          </Link>
        </li>
        {renderPageNumbers()}
        <li className="me-2">
          <Link
            data-pagination-link
            className="pagination__link"
            href="#"
            onClick={(e) => handlePageChange(currentPage + 1, e)}
            style={{ pointerEvents: currentPage === totalPages ? "none" : "auto" }}
            aria-label="Go to next page"
          >
            <i className="far fa-arrow-right" aria-hidden="true" />
          </Link>
        </li>
      </ul>
    </nav>
  );
};

export default Pagination;