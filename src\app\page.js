'use client'

import Layout from "../layout/Layout";
import Link from "next/link";
import Image from "next/image";

export default function HomePage() {
  return (
    <Layout header={2} >
      {/*====== Start Hero Section ======*/}
      <section className="hero-section">
        <div className="hero-wrapper-three">
          <div className="single-slider">
            <div
              className="image-layer bg_cover"
              style={{
                backgroundImage: "url(/assets/images/hero/vineyards.jpg)",
              }}
            />
            <div className="container">
              <div className="row align-items-center">
                <div className="col-xl-7">
                  <div className="hero-content text-white">
                    <span className="sub-title text-white">
                      Bem-vindo ao Descubra Cabo Verde
                    </span>
                    <h1>
                      Turismo, Viagens e Aventura
                    </h1>
                    <div className="hero-button">
                      <Link href="/about" className="main-btn primary-btn">
                        Explore Mais
                        <i className="fas fa-paper-plane" />
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="about-section pt-100 mt-100">
        <div className="container-fluid">
          <div className="row justify-content-center">
            <div className="col-xl-6 col-lg-9">
              <div className="about-content-box text-center mb-55">
                <div className="section-title mb-30">
                  <span className="sub-title">Sobre Descubra Cabo Verde</span>
                  <h2>Explore as Verdadeiras Aventuras e Viagens de Cabo Verde</h2>
                </div>
                <p>
                  Situado no Oceano Atlântico, este arquipélago encantador oferece
                  uma rica tapeçaria de paisagens, desde praias de areia branca deslumbrantes
                  aos picos vulcânicos dramáticos do Fogo.
                </p>
              </div>
            </div>
          </div>

          <div className="row">
            <div className="col-lg-3 col-md-6">
              <div className="single-features-item mb-40">
                <div className="img-holder">
                  <Image
                    src="/assets/images/features/hotel.png"
                    alt="Ícone de hotéis em Cabo Verde - Acomodações de luxo"
                    title="Hotéis e Resorts em Cabo Verde"
                    width={300}
                    height={200}
                    style={{
                      width: '100%',
                      height: 'auto',
                      borderRadius: '7px',
                      objectFit: 'cover'
                    }}
                  />
                  <div className="content">
                    <div className="text">
                      <h4 className="title">Hotéis</h4>
                      <Link href="/hotels" className="icon-btn">
                        <i className="far fa-arrow-right" />
                      </Link>
                    </div>
                    <p>Cabo Verde oferece uma gama diversa de hotéis, desde resorts de luxo...</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="col-lg-3 col-md-6">
              <div className="single-features-item mb-40">
                <div className="img-holder">
                  <Image
                    src="/assets/images/features/restaurant.png"
                    alt="Ícone de restaurantes em Cabo Verde - Gastronomia local"
                    title="Restaurantes e Culinária Cabo-verdiana"
                    width={300}
                    height={200}
                    style={{
                      width: '100%',
                      height: 'auto',
                      borderRadius: '7px',
                      objectFit: 'cover'
                    }}
                  />
                  <div className="content">
                    <div className="text">
                      <h4 className="title">Restaurantes</h4>
                      <Link href="/restaurants" className="icon-btn">
                        <i className="far fa-arrow-right" />
                      </Link>
                    </div>
                    <p>Os restaurantes cabo-verdianos mostram a rica diversidade culinária...</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="col-lg-3 col-md-6">
              <div className="single-features-item mb-40">
                <div className="img-holder">
                  <Image
                    src="/assets/images/features/destinations.png"
                    alt="Ícone de destinos em Cabo Verde - Ilhas paradisíacas"
                    title="Destinos Turísticos de Cabo Verde"
                    width={300}
                    height={200}
                    style={{
                      width: '100%',
                      height: 'auto',
                      borderRadius: '7px',
                      objectFit: 'cover'
                    }}
                  />
                  <div className="content">
                    <div className="text">
                      <h4 className="title">Destinos</h4>
                      <Link href="/destinations" className="icon-btn">
                        <i className="far fa-arrow-right" />
                      </Link>
                    </div>
                    <p>Os destinos de Cabo Verde vão desde as praias paradisíacas de Sal...</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="col-lg-3 col-md-6">
              <div className="single-features-item mb-40">
                <div className="img-holder">
                  <Image
                    src="/assets/images/features/activities.png"
                    alt="Ícone de atividades em Cabo Verde - Aventuras e experiências"
                    title="Atividades e Coisas para Fazer em Cabo Verde"
                    width={300}
                    height={200}
                    style={{
                      width: '100%',
                      height: 'auto',
                      borderRadius: '7px',
                      objectFit: 'cover'
                    }}
                  />
                  <div className="content">
                    <div className="text">
                      <h4 className="title">Coisas para Fazer</h4>
                      <Link href="/things-to-do" className="icon-btn">
                        <i className="far fa-arrow-right" />
                      </Link>
                    </div>
                    <p>Cabo Verde oferece uma variedade de atividades para visitantes, incluindo...</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  )
}