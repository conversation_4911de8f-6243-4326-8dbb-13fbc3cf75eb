import Link from 'next/link'
import Image from 'next/image';
const Footer = ({ bg, extraClass }) => {
  return (
    <footer
      className={`main-footer ${bg ? bg : "black"}-bg ${
        extraClass ? extraClass : ""
      }`}
    >
      <div className="container">
        {/*=== Footer CTA ===*/}

        {/*=== Footer Widget ===*/}
        <div className="footer-widget-area pt-30 pb-30">
          <div className="row">
            <div className="col-lg-3 col-md-6">
              {/*=== Footer Widget ===*/}
              <div className="footer-widget about-company-widget mb-40">
                <h4 className="widget-title">Sobre</h4>
                <div className="footer-content">
                  <p>
                  Este arquipélago encantador oferece uma rica tapeçaria de paisagens,
                  desde praias de areia branca deslumbrantes aos picos vulcânicos dramáticos do Fogo.
                  </p>
                  <Link href="/" className="footer-logo">
                    <Image
                      width={160}
                      height={70}
                      src="/assets/images/logo/guia-turistico-logo.png"
                      alt="Guia Turístico - Descubra Cabo Verde"
                      title="Descubra Cabo Verde - Seu guia turístico oficial"
                      style={{
                        width: 'auto',
                        height: '70px',
                        maxWidth: '160px',
                        objectFit: 'contain',
                        filter: bg === 'black' ? 'brightness(0) invert(1)' : 'none'
                      }}
                    />
                  </Link>
                </div>
              </div>
            </div>
            <div className="col-lg-5 col-md-6">
              {/*=== Footer Widget ===*/}
              <div className="footer-widget service-nav-widget mb-40 pl-lg-70">
                <h4 className="widget-title">Serviços</h4>
                <div className="footer-content">
                  <ul className="footer-widget-nav">
                    <li>
                      <Link href="/restaurants">Restaurantes</Link>
                    </li>
                    <li>
                      <Link href="/hotels">Hotéis</Link>
                    </li>
                    <li>
                      <Link href="/places">Destinos</Link>
                    </li>
                    <li>
                      <Link href="/about">Sobre Cabo Verde</Link>
                    </li>
                    <li>
                      <Link href="/places">Eventos</Link>
                    </li>
                  </ul>
                  <ul className="footer-widget-nav">
                    <li>
                      <Link href="/">Início</Link>
                    </li>
                    <li>
                      <Link href="/blog">Últimas Notícias &amp; Blog</Link>
                    </li>
                    <li>
                      <Link href="/contact">Contactar</Link>
                    </li>

                  </ul>
                </div>
              </div>
            </div>
            <div className="col-lg-4 col-md-6">
              {/*=== Footer Widget ===*/}
              <div className="footer-widget footer-newsletter-widget mb-40 pl-lg-100">
                <h4 className="widget-title">Newsletter</h4>
                <div className="footer-content">
                  <p>
                    Receba as últimas notícias e ofertas especiais de Cabo Verde
                    diretamente no seu email
                  </p>
                  <form>
                    <div className="form_group">
                      <label>
                        <i className="far fa-paper-plane" />
                      </label>
                      <input
                        type="email"
                        className="form_control"
                        placeholder="Endereço de Email"
                        name="email"
                        required
                      />
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/*=== Footer Copyright ===*/}
        <div className="footer-copyright">
          <div className="row">
            <div className="col-lg-6">
              {/*=== Footer Text ===*/}
              <div className="footer-text">
                <p>
                  Copyright@ 2024 <span style={{ color: "#F7921E" }}> Descubra Cabo Verde</span>,
                  Todos os Direitos Reservados
                </p>
              </div>
            </div>
            <div className="col-lg-6">
              {/*=== Footer Nav ===*/}
              <div className="footer-nav float-lg-end">
                <ul>
                  <li>
                    <Link href="/privacy-policy">Política de Privacidade</Link>
                  </li>
                  <li>
                    <Link href="faqs">Perguntas Frequentes</Link>
                  </li>
                  <li>
                    <Link href="/contact">Suporte</Link>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
export default Footer;
