import PageBanner from "../../components/PageBanner";
import Layout from "../../layout/Layout";
import Link from "next/link";
import Image from "next/image";

export const metadata = {
  title: "Contactar | Descubra Cabo Verde",
  description: "Contacte-nos se tiver alguma pergunta sobre viajar em Cabo Verde.",
  openGraph: {
    title: "Contactar | Descubra Cabo Verde",
    description: "Contacte-nos se tiver alguma pergunta sobre viajar em Cabo Verde.",
    url: "https://www.guiadecaboverde.cv/contact",
    images: ['/assets/images/logo/guia-turistico-logo.png'],
  },
};

const Contact = () => {
  return (
    <Layout header={2}>
        <PageBanner pageTitle={"Contactar"} />
        {/*====== Start Info Section ======*/}
        <section className="contact-info-section pt-100 pb-60">
          <div className="container">
            <div className="row justify-content-center">
              <div className="col-xl-8">
                {/*=== Section Title ===*/}
                <div className="section-title text-center mb-45 wow fadeInDown">
                  <span className="sub-title">Contactar</span>
                  <h3>Entre em Contacto Hoje</h3>
                </div>
              </div>
            </div>
            <div className="row justify-content-center">
              <div className="col-lg-4 col-md-6 col-sm-12">
                {/*=== Contact Info Item ===*/}
                <div className="contact-info-item text-center mb-40 wow fadeInUp">
                  <div className="icon">
                    <Image
                      width={100}
                      height={100}
                      src="/assets/images/icon/icon-1.png"
                      alt="Ícone de localização - Escritório em Praia, Santiago"
                      title="Localização do Escritório"
                    />
                  </div>
                  <div className="info">
                    <span className="title">Localização do Escritório</span>
                    <p>Avenida Cidade de Lisboa, Praia, Santiago</p>
                  </div>
                </div>
              </div>
              <div className="col-lg-4 col-md-6 col-sm-12">
                {/*=== Contact Info Item ===*/}
                <div className="contact-info-item text-center mb-40 wow fadeInDown">
                  <div className="icon">
                    <Image
                      width={100}
                      height={100}
                      src="/assets/images/icon/icon-2.png"
                      alt="Ícone de email - Contacto por email"
                      title="Endereço de Email"
                    />
                  </div>
                  <div className="info">
                    <span className="title">Endereço de Email</span>
                    <p>
                      <Link href="mailto:<EMAIL>">
                        <EMAIL>
                      </Link>
                    </p>
                  </div>
                </div>
              </div>
              <div className="col-lg-4 col-md-6 col-sm-12">
                {/*=== Contact Info Item ===*/}
                <div className="contact-info-item text-center mb-40 wow fadeInUp">
                  <div className="icon">
                    <Image
                      width={100}
                      height={100}
                      src="/assets/images/icon/icon-3.png"
                      alt="Ícone de telefone - Linha direta para contacto"
                      title="Linha Direta"
                    />
                  </div>
                  <div className="info">
                    <span className="title">Linha Direta</span>
                    <p>
                      <Link href="tel:(+238) 260 12 00">
                        (+238) 260 12 00
                      </Link>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/*====== End Info Section ======*/}
      </Layout>
  );
};

export default Contact;