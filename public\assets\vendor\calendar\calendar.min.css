
.buttons-container {
  cursor: pointer;
}

.week {
  overflow: hidden;
}

/* weekend */
.week .day:first-child, .week .day:last-child {
  /*color: orange;*/
}

/* sunday */
.week .day:first-child {
  /*color: red;*/
}

.day {
  display: inline-block;
  float: left;
  width: 14.28%;
  text-align: center;
  cursor: pointer;
  box-sizing: border-box;
}

.day.disabled span {
  color: #AAA;
}

/* vertical highlight */
.week .day.highlight span {
  /*color: blue;*/
}

.day.selected span {
  /*background: orange;*/
}

.day.today span {
  font-weight: bold;
}

.months-container.hidden, .weeks-container.hidden {
  display: none;
}

.months-wrapper {
  overflow: hidden;
}

.months-wrapper .month {
  display: inline-block;
  float: left;
  width: 25%;
  text-align: center;
  cursor: pointer;
}
.months-wrapper .month.one-third {
  width: 33.33%;
}


.buttons-container {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 10px 45px;
  border-bottom: 1px solid #eee;
}

.buttons-container .label-container {
  display: inline-block;
  -webkit-box-flex: 1;
  -moz-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  text-align: center;
  text-transform: uppercase;
  font-weight: bold;
}

.year-dropdown {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: none;
  font-size: inherit;
  font-weight: inherit;
  font-family: inherit;
  padding: 5px 20px;
}

.prev-button,
.next-button {
  background: transparent;
  border: none;
  padding: 10px;
}

.week {
  margin: 10px 0;
}
.weeks-wrapper.header {
  border-bottom: 1px solid #eee;
}

.week .day.header {
  font-weight: 600;
  text-transform: capitalize;
  font-size: 16px;
}

.day span {
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-weight: 600;
  vertical-align: middle;
  font-size: 16px;
}

.day.today span {
  position: relative;
  display: inline-block;
}

.week .day.selected span {
  background: #F7921E;
  color: white;
}

.week .day[disabled="disabled"] span {
  color: #aaa;
  cursor: not-allowed;
}

.months-wrapper .month span {
  display: inline-block;
  padding: 10px;
  text-transform: capitalize;
  margin-bottom: 10px;
}

.special-buttons {
  text-align: center;
  border-top: 1px solid #eee;
  padding-top: 10px;
}

.today-button {
  margin: 0 auto;
  background: transparent;
  border: none;
  padding: 5px;
}