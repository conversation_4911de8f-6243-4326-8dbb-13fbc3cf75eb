"use client";
import Link from "next/link";
import React, { useState, useEffect } from "react";
import axios from "axios";
import { Container, Row, Col } from "react-bootstrap";
import Pagination from "../Pagination/Pagination";
import { Image } from "react-bootstrap";
import PreLoader from "../../layout/PreLoader";
import SingleService from "../SingleService/SingleService";
import CuisineFilter from "../FilterCuisine/FilterCuisine";

// SingleRestaurant component



export default function RestaurantsPage() {
  const [restaurants, setRestaurants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const restaurantsPerPage = 10;

  useEffect(() => {
    const fetchRestaurants = async () => {
      try {
        const response = await axios.post(
          "https://api.yellowpageskenya.com/v1/search",
          {
            searchParameters: {
              LINGUA_ID: 1,
              PESQUISA_F: "Restaurantes",
              TITULO_ID: 452,
              country: "CBV",
            },
          }
        );
        setRestaurants(response.data.results || []);

        setLoading(false);
      } catch (err) {
        setError("Failed to fetch restaurants");
        setLoading(false);
      }
    };

    fetchRestaurants();
  }, []);

  const indexOfLastRestaurant = currentPage * restaurantsPerPage;
  const indexOfFirstRestaurant = indexOfLastRestaurant - restaurantsPerPage;
  const currentRestaurants = restaurants.slice(
    indexOfFirstRestaurant,
    indexOfLastRestaurant
  );

  const onPageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  if (loading) {
    return (
      <div
        className="container"
        style={{display:"flex",
          justifyContent:"center",
          alignItems:"center",
          flexDirection:"column",
        }}
        aria-live="polite"
      >
        <PreLoader />
        <div style={{ textAlign: "center", marginTop: "20px" }}>
          <span className="loader"></span>
          <p>Fetching the best restaurants for you...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-10 text-red-500" aria-live="assertive">
        {error}
      </div>
    );
  }

  return (
    <section className="team-section pt-20">
    <CuisineFilter data={currentRestaurants} />
      <Container>
        <Row className="restaurant-parent-container">

        </Row>

        {/* <div className="mt-10 mb-20 flex justify-center">
          <Pagination
            currentPage={currentPage}
            totalPages={Math.ceil(restaurants.length / restaurantsPerPage)}
            onPageChange={onPageChange}
          />
        </div> */}
      </Container>
    </section>
  );
}

