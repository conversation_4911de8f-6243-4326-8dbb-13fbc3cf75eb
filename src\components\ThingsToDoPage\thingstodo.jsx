'use client';
import React, { useState } from "react";
import thingsToDo from "../../data/thingsToDo";
import Masonry from "react-masonry-css";
import { <PERSON>, Button } from "react-bootstrap";
import { useRouter } from "next/navigation";
import Pagination from "../Pagination/Pagination";
import styles from "./ThingsToDoPage.module.css";

const ThingsToDoPage = () => {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 8;
  const totalPages = Math.ceil(thingsToDo.length / itemsPerPage);

  const masonryBreakpoints = {
    default: 3,
    1100: 2,
    700: 1,
  };

  const handleReadMore = (id) => {
    router.push(`/things-to-do-details/${id}`);
  };

  // Calculate the current page's items
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = thingsToDo.slice(startIndex, endIndex);

  return (
    <div className="container my-4">
      <Masonry
        breakpointCols={masonryBreakpoints}
        className={styles.masonryGrid}
        columnClassName={styles.masonryGridColumn}
      >
        {currentItems.map((activity) => (
          <Card key={activity.id} className={`mb-4 ${styles.cardStyle}`}>
            <Card.Img
              src={activity.image}
              alt={`${activity.title} - ${activity.category} em ${activity.location}, Cabo Verde`}
              title={`Descubra ${activity.title} em ${activity.location}`}
              className={styles.cardImage}
            />

            <Card.Body>
              <Card.Title>{activity.title}</Card.Title>
              <Card.Text>{activity.description.substring(0, 100)}...</Card.Text>
              <Button
                style={{ backgroundColor: "#fa4836", border: "none" }}
                onClick={() => handleReadMore(activity.id)}
              >
                Read More
              </Button>
            </Card.Body>
          </Card>
        ))}
      </Masonry>

      {/* Pagination Component */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={setCurrentPage}
      />
    </div>
  );
};

export default ThingsToDoPage;
